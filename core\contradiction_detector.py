"""Contradiction Detection Module

This module provides functionality to detect logical contradictions in knowledge bases
and statements. It identifies conflicts between facts, rules, and assertions.
"""

from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import time
from collections import defaultdict

class ContradictionType(Enum):
    """Types of contradictions that can be detected."""
    DIRECT = "direct"  # A and not A
    PROPERTY = "property"  # X is Y and X is not Y
    ACTION = "action"  # X can do Y and X cannot do Y
    INHERITANCE = "inheritance"  # X is A and X is B where A and B are mutually exclusive
    TEMPORAL = "temporal"  # X happened at time T1 and X did not happen at time T1
    LOGICAL = "logical"  # Logical rule violation
    SEMANTIC = "semantic"  # Semantic meaning conflict

class ContradictionSeverity(Enum):
    """Severity levels for contradictions."""
    CRITICAL = "critical"  # Direct logical contradiction
    HIGH = "high"  # Strong semantic conflict
    MEDIUM = "medium"  # Possible conflict requiring review
    LOW = "low"  # Minor inconsistency

@dataclass
class Fact:
    """Represents a fact in the knowledge base."""
    subject: str
    predicate: str
    object: str
    negated: bool = False
    confidence: float = 1.0
    timestamp: float = None
    source: str = "unknown"
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
    
    def __str__(self) -> str:
        neg = "not " if self.negated else ""
        return f"{self.subject} {neg}{self.predicate} {self.object}"
    
    def __hash__(self) -> int:
        return hash((self.subject, self.predicate, self.object, self.negated))
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Fact):
            return False
        return (self.subject == other.subject and 
                self.predicate == other.predicate and 
                self.object == other.object and 
                self.negated == other.negated)

@dataclass
class Contradiction:
    """Represents a detected contradiction."""
    fact1: Fact
    fact2: Fact
    contradiction_type: ContradictionType
    severity: ContradictionSeverity
    explanation: str
    confidence: float
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
    
    def __str__(self) -> str:
        return f"{self.contradiction_type.value.upper()}: {self.fact1} vs {self.fact2}"

class ContradictionDetector:
    """Main class for detecting contradictions in knowledge bases."""
    
    def __init__(self):
        self.facts: Set[Fact] = set()
        self.contradictions: List[Contradiction] = []
        self.exclusion_rules: Dict[str, Set[str]] = defaultdict(set)
        self.hierarchy: Dict[str, Set[str]] = defaultdict(set)  # parent -> children
        self.cache: Dict[str, List[Contradiction]] = {}
        self.stats = {
            'facts_processed': 0,
            'contradictions_found': 0,
            'cache_hits': 0,
            'detection_time': 0.0
        }
    
    def add_fact(self, fact: Fact) -> List[Contradiction]:
        """Add a fact and detect any contradictions it creates."""
        start_time = time.time()
        
        # Check for contradictions before adding
        new_contradictions = self._detect_contradictions_for_fact(fact)
        
        # Add the fact
        self.facts.add(fact)
        self.contradictions.extend(new_contradictions)
        
        # Update statistics
        self.stats['facts_processed'] += 1
        self.stats['contradictions_found'] += len(new_contradictions)
        self.stats['detection_time'] += time.time() - start_time
        
        return new_contradictions
    
    def add_facts(self, facts: List[Fact]) -> List[Contradiction]:
        """Add multiple facts and detect contradictions."""
        all_contradictions = []
        for fact in facts:
            contradictions = self.add_fact(fact)
            all_contradictions.extend(contradictions)
        return all_contradictions
    
    def remove_fact(self, fact: Fact) -> bool:
        """Remove a fact from the knowledge base."""
        if fact in self.facts:
            self.facts.remove(fact)
            # Remove contradictions involving this fact
            self.contradictions = [
                c for c in self.contradictions 
                if c.fact1 != fact and c.fact2 != fact
            ]
            self._clear_cache()
            return True
        return False
    
    def detect_all_contradictions(self) -> List[Contradiction]:
        """Detect all contradictions in the current knowledge base."""
        cache_key = f"all_{len(self.facts)}"
        if cache_key in self.cache:
            self.stats['cache_hits'] += 1
            return self.cache[cache_key]
        
        start_time = time.time()
        all_contradictions = []
        
        facts_list = list(self.facts)
        for i, fact1 in enumerate(facts_list):
            for fact2 in facts_list[i+1:]:
                contradiction = self._check_contradiction_pair(fact1, fact2)
                if contradiction:
                    all_contradictions.append(contradiction)
        
        self.stats['detection_time'] += time.time() - start_time
        self.cache[cache_key] = all_contradictions
        return all_contradictions
    
    def _detect_contradictions_for_fact(self, new_fact: Fact) -> List[Contradiction]:
        """Detect contradictions for a specific fact against existing facts."""
        contradictions = []
        
        for existing_fact in self.facts:
            contradiction = self._check_contradiction_pair(new_fact, existing_fact)
            if contradiction:
                contradictions.append(contradiction)
        
        return contradictions
    
    def _check_contradiction_pair(self, fact1: Fact, fact2: Fact) -> Optional[Contradiction]:
        """Check if two facts contradict each other."""
        # Action contradiction: X can do Y and X cannot do Y (check before direct contradiction)
        if (fact1.subject == fact2.subject and 
            fact1.predicate == "can" and fact2.predicate == "can" and 
            fact1.object == fact2.object and 
            fact1.negated != fact2.negated):
            
            return Contradiction(
                fact1=fact1,
                fact2=fact2,
                contradiction_type=ContradictionType.ACTION,
                severity=ContradictionSeverity.HIGH,
                explanation=f"Action contradiction: {fact1} contradicts {fact2}",
                confidence=min(fact1.confidence, fact2.confidence)
            )
        
        # Direct contradiction: A and not A (for non-action predicates)
        if (fact1.subject == fact2.subject and 
            fact1.predicate == fact2.predicate and 
            fact1.object == fact2.object and 
            fact1.negated != fact2.negated and
            fact1.predicate != "can"):
            
            return Contradiction(
                fact1=fact1,
                fact2=fact2,
                contradiction_type=ContradictionType.DIRECT,
                severity=ContradictionSeverity.CRITICAL,
                explanation=f"Direct contradiction: {fact1} contradicts {fact2}",
                confidence=min(fact1.confidence, fact2.confidence)
            )
        
        # Property contradiction: X is Y and X is Z (where Y and Z are exclusive)
        if (fact1.subject == fact2.subject and 
            fact1.predicate == "is" and fact2.predicate == "is" and 
            fact1.object != fact2.object and 
            not fact1.negated and not fact2.negated):
            
            if self._are_mutually_exclusive(fact1.object, fact2.object):
                return Contradiction(
                    fact1=fact1,
                    fact2=fact2,
                    contradiction_type=ContradictionType.PROPERTY,
                    severity=ContradictionSeverity.HIGH,
                    explanation=f"Property contradiction: {fact1.object} and {fact2.object} are mutually exclusive",
                    confidence=min(fact1.confidence, fact2.confidence) * 0.8
                )
        
        # Inheritance contradiction
        if (fact1.subject == fact2.subject and 
            fact1.predicate == "is_a" and fact2.predicate == "is_a" and 
            fact1.object != fact2.object and 
            not fact1.negated and not fact2.negated):
            
            if self._are_incompatible_types(fact1.object, fact2.object):
                return Contradiction(
                    fact1=fact1,
                    fact2=fact2,
                    contradiction_type=ContradictionType.INHERITANCE,
                    severity=ContradictionSeverity.HIGH,
                    explanation=f"Inheritance contradiction: {fact1.object} and {fact2.object} are incompatible types",
                    confidence=min(fact1.confidence, fact2.confidence) * 0.7
                )
        
        return None
    
    def _are_mutually_exclusive(self, type1: str, type2: str) -> bool:
        """Check if two types are mutually exclusive."""
        return type2 in self.exclusion_rules.get(type1, set()) or \
               type1 in self.exclusion_rules.get(type2, set())
    
    def _are_incompatible_types(self, type1: str, type2: str) -> bool:
        """Check if two types are incompatible in the hierarchy."""
        # Check if they're in different branches of the hierarchy
        # This is a simplified check - in practice, you'd want more sophisticated logic
        incompatible_pairs = {
            ('animal', 'plant'),
            ('living_thing', 'object'),
            ('physical_entity', 'abstract_entity'),
            ('mammal', 'bird'),
            ('mammal', 'fish'),
            ('bird', 'fish')
        }
        
        return (type1, type2) in incompatible_pairs or (type2, type1) in incompatible_pairs
    
    def add_exclusion_rule(self, type1: str, type2: str):
        """Add a mutual exclusion rule between two types."""
        self.exclusion_rules[type1].add(type2)
        self.exclusion_rules[type2].add(type1)
        self._clear_cache()
    
    def add_hierarchy_relation(self, parent: str, child: str):
        """Add a hierarchical relationship."""
        self.hierarchy[parent].add(child)
        self._clear_cache()
    
    def get_contradictions_by_type(self, contradiction_type: ContradictionType) -> List[Contradiction]:
        """Get all contradictions of a specific type."""
        return [c for c in self.contradictions if c.contradiction_type == contradiction_type]
    
    def get_contradictions_by_severity(self, severity: ContradictionSeverity) -> List[Contradiction]:
        """Get all contradictions of a specific severity."""
        return [c for c in self.contradictions if c.severity == severity]
    
    def get_contradictions_involving_fact(self, fact: Fact) -> List[Contradiction]:
        """Get all contradictions involving a specific fact."""
        return [c for c in self.contradictions if c.fact1 == fact or c.fact2 == fact]
    
    def resolve_contradiction(self, contradiction: Contradiction, keep_fact: Fact) -> bool:
        """Resolve a contradiction by keeping one fact and removing the other."""
        if contradiction not in self.contradictions:
            return False
        
        # Remove the contradiction
        self.contradictions.remove(contradiction)
        
        # Remove the fact we don't want to keep
        if keep_fact == contradiction.fact1:
            self.remove_fact(contradiction.fact2)
        elif keep_fact == contradiction.fact2:
            self.remove_fact(contradiction.fact1)
        else:
            return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get detection statistics."""
        return {
            **self.stats,
            'total_facts': len(self.facts),
            'total_contradictions': len(self.contradictions),
            'critical_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.CRITICAL)),
            'high_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.HIGH)),
            'medium_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.MEDIUM)),
            'low_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.LOW))
        }
    
    def _clear_cache(self):
        """Clear the contradiction detection cache."""
        self.cache.clear()
    
    def clear_all(self):
        """Clear all facts and contradictions."""
        self.facts.clear()
        self.contradictions.clear()
        self._clear_cache()
        self.stats = {
            'facts_processed': 0,
            'contradictions_found': 0,
            'cache_hits': 0,
            'detection_time': 0.0
        }
    
    def export_contradictions(self) -> List[Dict[str, Any]]:
        """Export contradictions as a list of dictionaries."""
        return [
            {
                'fact1': str(c.fact1),
                'fact2': str(c.fact2),
                'type': c.contradiction_type.value,
                'severity': c.severity.value,
                'explanation': c.explanation,
                'confidence': c.confidence,
                'timestamp': c.timestamp
            }
            for c in self.contradictions
        ]
    
    def import_facts_from_dict(self, facts_data: List[Dict[str, Any]]) -> List[Contradiction]:
        """Import facts from a list of dictionaries."""
        facts = []
        for fact_data in facts_data:
            fact = Fact(
                subject=fact_data['subject'],
                predicate=fact_data['predicate'],
                object=fact_data['object'],
                negated=fact_data.get('negated', False),
                confidence=fact_data.get('confidence', 1.0),
                source=fact_data.get('source', 'unknown')
            )
            facts.append(fact)
        
        return self.add_facts(facts)