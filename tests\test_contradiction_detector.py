"""Tests for Contradiction Detection Module

Comprehensive test suite for the contradiction detection functionality.
"""

import unittest
import time
from typing import List, Dict, Any

try:
    from core.contradiction_detector import (
        ContradictionDetector, Fact, Contradiction, 
        ContradictionType, ContradictionSeverity
    )
except ImportError:
    # Fallback implementation for testing
    from typing import Dict, List, Set, Tuple, Optional, Any
    from dataclasses import dataclass
    from enum import Enum
    import time
    from collections import defaultdict

    class ContradictionType(Enum):
        DIRECT = "direct"
        PROPERTY = "property"
        ACTION = "action"
        INHERITANCE = "inheritance"
        TEMPORAL = "temporal"
        LOGICAL = "logical"
        SEMANTIC = "semantic"

    class ContradictionSeverity(Enum):
        CRITICAL = "critical"
        HIGH = "high"
        MEDIUM = "medium"
        LOW = "low"

    @dataclass
    class Fact:
        subject: str
        predicate: str
        object: str
        negated: bool = False
        confidence: float = 1.0
        timestamp: float = None
        source: str = "unknown"
        
        def __post_init__(self):
            if self.timestamp is None:
                self.timestamp = time.time()
        
        def __str__(self) -> str:
            neg = "not " if self.negated else ""
            return f"{self.subject} {neg}{self.predicate} {self.object}"
        
        def __hash__(self) -> int:
            return hash((self.subject, self.predicate, self.object, self.negated))
        
        def __eq__(self, other) -> bool:
            if not isinstance(other, Fact):
                return False
            return (self.subject == other.subject and 
                    self.predicate == other.predicate and 
                    self.object == other.object and 
                    self.negated == other.negated)

    @dataclass
    class Contradiction:
        fact1: Fact
        fact2: Fact
        contradiction_type: ContradictionType
        severity: ContradictionSeverity
        explanation: str
        confidence: float
        timestamp: float = None
        
        def __post_init__(self):
            if self.timestamp is None:
                self.timestamp = time.time()
        
        def __str__(self) -> str:
            return f"{self.contradiction_type.value.upper()}: {self.fact1} vs {self.fact2}"

    class ContradictionDetector:
        def __init__(self):
            self.facts: Set[Fact] = set()
            self.contradictions: List[Contradiction] = []
            self.exclusion_rules: Dict[str, Set[str]] = defaultdict(set)
            self.hierarchy: Dict[str, Set[str]] = defaultdict(set)
            self.cache: Dict[str, List[Contradiction]] = {}
            self.stats = {
                'facts_processed': 0,
                'contradictions_found': 0,
                'cache_hits': 0,
                'detection_time': 0.0
            }
        
        def add_fact(self, fact: Fact) -> List[Contradiction]:
            start_time = time.time()
            new_contradictions = self._detect_contradictions_for_fact(fact)
            self.facts.add(fact)
            self.contradictions.extend(new_contradictions)
            self.stats['facts_processed'] += 1
            self.stats['contradictions_found'] += len(new_contradictions)
            self.stats['detection_time'] += time.time() - start_time
            return new_contradictions
        
        def add_facts(self, facts: List[Fact]) -> List[Contradiction]:
            all_contradictions = []
            for fact in facts:
                contradictions = self.add_fact(fact)
                all_contradictions.extend(contradictions)
            return all_contradictions
        
        def remove_fact(self, fact: Fact) -> bool:
            if fact in self.facts:
                self.facts.remove(fact)
                self.contradictions = [
                    c for c in self.contradictions 
                    if c.fact1 != fact and c.fact2 != fact
                ]
                self._clear_cache()
                return True
            return False
        
        def detect_all_contradictions(self) -> List[Contradiction]:
            cache_key = f"all_{len(self.facts)}"
            if cache_key in self.cache:
                self.stats['cache_hits'] += 1
                return self.cache[cache_key]
            
            start_time = time.time()
            all_contradictions = []
            
            facts_list = list(self.facts)
            for i, fact1 in enumerate(facts_list):
                for fact2 in facts_list[i+1:]:
                    contradiction = self._check_contradiction_pair(fact1, fact2)
                    if contradiction:
                        all_contradictions.append(contradiction)
            
            self.stats['detection_time'] += time.time() - start_time
            self.cache[cache_key] = all_contradictions
            return all_contradictions
        
        def _detect_contradictions_for_fact(self, new_fact: Fact) -> List[Contradiction]:
            contradictions = []
            for existing_fact in self.facts:
                contradiction = self._check_contradiction_pair(new_fact, existing_fact)
                if contradiction:
                    contradictions.append(contradiction)
            return contradictions
        
        def _check_contradiction_pair(self, fact1: Fact, fact2: Fact) -> Optional[Contradiction]:
            # Action contradiction (check before direct contradiction for "can" predicate)
            if (fact1.subject == fact2.subject and 
                fact1.predicate == "can" and fact2.predicate == "can" and 
                fact1.object == fact2.object and 
                fact1.negated != fact2.negated):
                
                return Contradiction(
                    fact1=fact1,
                    fact2=fact2,
                    contradiction_type=ContradictionType.ACTION,
                    severity=ContradictionSeverity.HIGH,
                    explanation=f"Action contradiction: {fact1} contradicts {fact2}",
                    confidence=min(fact1.confidence, fact2.confidence)
                )
            
            # Direct contradiction (for non-action predicates)
            if (fact1.subject == fact2.subject and 
                fact1.predicate == fact2.predicate and 
                fact1.object == fact2.object and 
                fact1.negated != fact2.negated and
                fact1.predicate != "can"):
                
                return Contradiction(
                    fact1=fact1,
                    fact2=fact2,
                    contradiction_type=ContradictionType.DIRECT,
                    severity=ContradictionSeverity.CRITICAL,
                    explanation=f"Direct contradiction: {fact1} contradicts {fact2}",
                    confidence=min(fact1.confidence, fact2.confidence)
                )
            
            # Property contradiction
            if (fact1.subject == fact2.subject and 
                fact1.predicate == "is" and fact2.predicate == "is" and 
                fact1.object != fact2.object and 
                not fact1.negated and not fact2.negated):
                
                if self._are_mutually_exclusive(fact1.object, fact2.object):
                    return Contradiction(
                        fact1=fact1,
                        fact2=fact2,
                        contradiction_type=ContradictionType.PROPERTY,
                        severity=ContradictionSeverity.HIGH,
                        explanation=f"Property contradiction: {fact1.object} and {fact2.object} are mutually exclusive",
                        confidence=min(fact1.confidence, fact2.confidence) * 0.8
                    )
            
            # Inheritance contradiction
            if (fact1.subject == fact2.subject and 
                fact1.predicate == "is_a" and fact2.predicate == "is_a" and 
                fact1.object != fact2.object and 
                not fact1.negated and not fact2.negated):
                
                if self._are_incompatible_types(fact1.object, fact2.object):
                    return Contradiction(
                        fact1=fact1,
                        fact2=fact2,
                        contradiction_type=ContradictionType.INHERITANCE,
                        severity=ContradictionSeverity.HIGH,
                        explanation=f"Inheritance contradiction: {fact1.object} and {fact2.object} are incompatible types",
                        confidence=min(fact1.confidence, fact2.confidence) * 0.7
                    )
            
            return None
        
        def _are_mutually_exclusive(self, type1: str, type2: str) -> bool:
            return type2 in self.exclusion_rules.get(type1, set()) or \
                   type1 in self.exclusion_rules.get(type2, set())
        
        def _are_incompatible_types(self, type1: str, type2: str) -> bool:
            incompatible_pairs = {
                ('animal', 'plant'),
                ('living_thing', 'object'),
                ('physical_entity', 'abstract_entity'),
                ('mammal', 'bird'),
                ('mammal', 'fish'),
                ('bird', 'fish')
            }
            return (type1, type2) in incompatible_pairs or (type2, type1) in incompatible_pairs
        
        def add_exclusion_rule(self, type1: str, type2: str):
            self.exclusion_rules[type1].add(type2)
            self.exclusion_rules[type2].add(type1)
            self._clear_cache()
        
        def add_hierarchy_relation(self, parent: str, child: str):
            self.hierarchy[parent].add(child)
            self._clear_cache()
        
        def get_contradictions_by_type(self, contradiction_type: ContradictionType) -> List[Contradiction]:
            return [c for c in self.contradictions if c.contradiction_type == contradiction_type]
        
        def get_contradictions_by_severity(self, severity: ContradictionSeverity) -> List[Contradiction]:
            return [c for c in self.contradictions if c.severity == severity]
        
        def get_contradictions_involving_fact(self, fact: Fact) -> List[Contradiction]:
            return [c for c in self.contradictions if c.fact1 == fact or c.fact2 == fact]
        
        def resolve_contradiction(self, contradiction: Contradiction, keep_fact: Fact) -> bool:
            if contradiction not in self.contradictions:
                return False
            
            self.contradictions.remove(contradiction)
            
            if keep_fact == contradiction.fact1:
                self.remove_fact(contradiction.fact2)
            elif keep_fact == contradiction.fact2:
                self.remove_fact(contradiction.fact1)
            else:
                return False
            
            return True
        
        def get_statistics(self) -> Dict[str, Any]:
            return {
                **self.stats,
                'total_facts': len(self.facts),
                'total_contradictions': len(self.contradictions),
                'critical_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.CRITICAL)),
                'high_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.HIGH)),
                'medium_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.MEDIUM)),
                'low_contradictions': len(self.get_contradictions_by_severity(ContradictionSeverity.LOW))
            }
        
        def _clear_cache(self):
            self.cache.clear()
        
        def clear_all(self):
            self.facts.clear()
            self.contradictions.clear()
            self._clear_cache()
            self.stats = {
                'facts_processed': 0,
                'contradictions_found': 0,
                'cache_hits': 0,
                'detection_time': 0.0
            }
        
        def export_contradictions(self) -> List[Dict[str, Any]]:
            return [
                {
                    'fact1': str(c.fact1),
                    'fact2': str(c.fact2),
                    'type': c.contradiction_type.value,
                    'severity': c.severity.value,
                    'explanation': c.explanation,
                    'confidence': c.confidence,
                    'timestamp': c.timestamp
                }
                for c in self.contradictions
            ]
        
        def import_facts_from_dict(self, facts_data: List[Dict[str, Any]]) -> List[Contradiction]:
            facts = []
            for fact_data in facts_data:
                fact = Fact(
                    subject=fact_data['subject'],
                    predicate=fact_data['predicate'],
                    object=fact_data['object'],
                    negated=fact_data.get('negated', False),
                    confidence=fact_data.get('confidence', 1.0),
                    source=fact_data.get('source', 'unknown')
                )
                facts.append(fact)
            
            return self.add_facts(facts)

class TestFact(unittest.TestCase):
    """Test the Fact class."""
    
    def test_fact_creation(self):
        """Test basic fact creation."""
        fact = Fact("cat", "is", "animal")
        self.assertEqual(fact.subject, "cat")
        self.assertEqual(fact.predicate, "is")
        self.assertEqual(fact.object, "animal")
        self.assertFalse(fact.negated)
        self.assertEqual(fact.confidence, 1.0)
        self.assertIsNotNone(fact.timestamp)
    
    def test_fact_negation(self):
        """Test negated facts."""
        fact = Fact("cat", "is", "dog", negated=True)
        self.assertTrue(fact.negated)
        self.assertEqual(str(fact), "cat not is dog")
    
    def test_fact_equality(self):
        """Test fact equality comparison."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal")
        fact3 = Fact("cat", "is", "animal", negated=True)
        
        self.assertEqual(fact1, fact2)
        self.assertNotEqual(fact1, fact3)
    
    def test_fact_hash(self):
        """Test fact hashing for set operations."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal")
        
        fact_set = {fact1, fact2}
        self.assertEqual(len(fact_set), 1)  # Should be deduplicated

class TestContradictionDetector(unittest.TestCase):
    """Test the ContradictionDetector class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = ContradictionDetector()
    
    def test_detector_initialization(self):
        """Test detector initialization."""
        self.assertEqual(len(self.detector.facts), 0)
        self.assertEqual(len(self.detector.contradictions), 0)
        stats = self.detector.get_statistics()
        self.assertEqual(stats['total_facts'], 0)
        self.assertEqual(stats['total_contradictions'], 0)
    
    def test_add_single_fact(self):
        """Test adding a single fact."""
        fact = Fact("cat", "is", "animal")
        contradictions = self.detector.add_fact(fact)
        
        self.assertEqual(len(contradictions), 0)
        self.assertEqual(len(self.detector.facts), 1)
        self.assertIn(fact, self.detector.facts)
    
    def test_direct_contradiction(self):
        """Test detection of direct contradictions."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        contradictions = self.detector.add_fact(fact2)
        
        self.assertEqual(len(contradictions), 1)
        contradiction = contradictions[0]
        self.assertEqual(contradiction.contradiction_type, ContradictionType.DIRECT)
        self.assertEqual(contradiction.severity, ContradictionSeverity.CRITICAL)
    
    def test_property_contradiction(self):
        """Test detection of property contradictions."""
        # Set up exclusion rule
        self.detector.add_exclusion_rule("red", "blue")
        
        fact1 = Fact("ball", "is", "red")
        fact2 = Fact("ball", "is", "blue")
        
        self.detector.add_fact(fact1)
        contradictions = self.detector.add_fact(fact2)
        
        self.assertEqual(len(contradictions), 1)
        contradiction = contradictions[0]
        self.assertEqual(contradiction.contradiction_type, ContradictionType.PROPERTY)
        self.assertEqual(contradiction.severity, ContradictionSeverity.HIGH)
    
    def test_action_contradiction(self):
        """Test detection of action contradictions."""
        fact1 = Fact("cat", "can", "fly")
        fact2 = Fact("cat", "can", "fly", negated=True)
        
        self.detector.add_fact(fact1)
        contradictions = self.detector.add_fact(fact2)
        
        self.assertEqual(len(contradictions), 1)
        contradiction = contradictions[0]
        self.assertEqual(contradiction.contradiction_type, ContradictionType.ACTION)
        self.assertEqual(contradiction.severity, ContradictionSeverity.HIGH)
    
    def test_inheritance_contradiction(self):
        """Test detection of inheritance contradictions."""
        fact1 = Fact("fluffy", "is_a", "mammal")
        fact2 = Fact("fluffy", "is_a", "bird")
        
        self.detector.add_fact(fact1)
        contradictions = self.detector.add_fact(fact2)
        
        self.assertEqual(len(contradictions), 1)
        contradiction = contradictions[0]
        self.assertEqual(contradiction.contradiction_type, ContradictionType.INHERITANCE)
        self.assertEqual(contradiction.severity, ContradictionSeverity.HIGH)
    
    def test_no_contradiction(self):
        """Test that compatible facts don't create contradictions."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "can", "meow")
        fact3 = Fact("dog", "is", "animal")
        
        self.detector.add_fact(fact1)
        contradictions1 = self.detector.add_fact(fact2)
        contradictions2 = self.detector.add_fact(fact3)
        
        self.assertEqual(len(contradictions1), 0)
        self.assertEqual(len(contradictions2), 0)
        self.assertEqual(len(self.detector.contradictions), 0)
    
    def test_multiple_facts(self):
        """Test adding multiple facts at once."""
        facts = [
            Fact("cat", "is", "animal"),
            Fact("dog", "is", "animal"),
            Fact("cat", "can", "meow")
        ]
        
        contradictions = self.detector.add_facts(facts)
        
        self.assertEqual(len(contradictions), 0)
        self.assertEqual(len(self.detector.facts), 3)
    
    def test_remove_fact(self):
        """Test removing facts."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        
        # Should have one contradiction
        self.assertEqual(len(self.detector.contradictions), 1)
        
        # Remove one fact
        removed = self.detector.remove_fact(fact1)
        self.assertTrue(removed)
        
        # Contradiction should be gone
        self.assertEqual(len(self.detector.contradictions), 0)
        self.assertEqual(len(self.detector.facts), 1)
    
    def test_detect_all_contradictions(self):
        """Test detecting all contradictions in knowledge base."""
        facts = [
            Fact("cat", "is", "animal"),
            Fact("cat", "is", "animal", negated=True),
            Fact("dog", "can", "fly"),
            Fact("dog", "can", "fly", negated=True)
        ]
        
        # Add facts without detecting contradictions individually
        for fact in facts:
            self.detector.facts.add(fact)
        
        # Now detect all contradictions
        contradictions = self.detector.detect_all_contradictions()
        
        self.assertEqual(len(contradictions), 2)
    
    def test_get_contradictions_by_type(self):
        """Test filtering contradictions by type."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        fact3 = Fact("dog", "can", "fly")
        fact4 = Fact("dog", "can", "fly", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        self.detector.add_fact(fact3)
        self.detector.add_fact(fact4)
        
        direct_contradictions = self.detector.get_contradictions_by_type(ContradictionType.DIRECT)
        action_contradictions = self.detector.get_contradictions_by_type(ContradictionType.ACTION)
        
        self.assertEqual(len(direct_contradictions), 1)
        self.assertEqual(len(action_contradictions), 1)
    
    def test_get_contradictions_by_severity(self):
        """Test filtering contradictions by severity."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        
        critical_contradictions = self.detector.get_contradictions_by_severity(ContradictionSeverity.CRITICAL)
        
        self.assertEqual(len(critical_contradictions), 1)
    
    def test_get_contradictions_involving_fact(self):
        """Test finding contradictions involving a specific fact."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        fact3 = Fact("dog", "is", "animal")
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        self.detector.add_fact(fact3)
        
        contradictions = self.detector.get_contradictions_involving_fact(fact1)
        
        self.assertEqual(len(contradictions), 1)
        self.assertTrue(contradictions[0].fact1 == fact1 or contradictions[0].fact2 == fact1)
    
    def test_resolve_contradiction(self):
        """Test resolving contradictions."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        
        contradiction = self.detector.contradictions[0]
        
        # Resolve by keeping fact1
        resolved = self.detector.resolve_contradiction(contradiction, fact1)
        
        self.assertTrue(resolved)
        self.assertEqual(len(self.detector.contradictions), 0)
        self.assertIn(fact1, self.detector.facts)
        self.assertNotIn(fact2, self.detector.facts)
    
    def test_exclusion_rules(self):
        """Test adding and using exclusion rules."""
        self.detector.add_exclusion_rule("hot", "cold")
        
        fact1 = Fact("water", "is", "hot")
        fact2 = Fact("water", "is", "cold")
        
        self.detector.add_fact(fact1)
        contradictions = self.detector.add_fact(fact2)
        
        self.assertEqual(len(contradictions), 1)
        self.assertEqual(contradictions[0].contradiction_type, ContradictionType.PROPERTY)
    
    def test_hierarchy_relations(self):
        """Test adding hierarchy relations."""
        self.detector.add_hierarchy_relation("animal", "mammal")
        self.detector.add_hierarchy_relation("mammal", "cat")
        
        # This is mainly for testing the interface - actual hierarchy logic would be more complex
        self.assertIn("mammal", self.detector.hierarchy["animal"])
        self.assertIn("cat", self.detector.hierarchy["mammal"])
    
    def test_statistics(self):
        """Test statistics collection."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        
        stats = self.detector.get_statistics()
        
        self.assertEqual(stats['facts_processed'], 2)
        self.assertEqual(stats['contradictions_found'], 1)
        self.assertEqual(stats['total_facts'], 2)
        self.assertEqual(stats['total_contradictions'], 1)
        self.assertEqual(stats['critical_contradictions'], 1)
    
    def test_cache_functionality(self):
        """Test caching of contradiction detection."""
        facts = [
            Fact("cat", "is", "animal"),
            Fact("dog", "is", "animal")
        ]
        
        for fact in facts:
            self.detector.facts.add(fact)
        
        # First call should populate cache
        contradictions1 = self.detector.detect_all_contradictions()
        
        # Second call should use cache
        contradictions2 = self.detector.detect_all_contradictions()
        
        self.assertEqual(contradictions1, contradictions2)
        self.assertGreater(self.detector.stats['cache_hits'], 0)
    
    def test_clear_all(self):
        """Test clearing all data."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        
        self.detector.clear_all()
        
        self.assertEqual(len(self.detector.facts), 0)
        self.assertEqual(len(self.detector.contradictions), 0)
        stats = self.detector.get_statistics()
        self.assertEqual(stats['facts_processed'], 0)
    
    def test_export_contradictions(self):
        """Test exporting contradictions."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        self.detector.add_fact(fact1)
        self.detector.add_fact(fact2)
        
        exported = self.detector.export_contradictions()
        
        self.assertEqual(len(exported), 1)
        self.assertIn('fact1', exported[0])
        self.assertIn('fact2', exported[0])
        self.assertIn('type', exported[0])
        self.assertIn('severity', exported[0])
    
    def test_import_facts_from_dict(self):
        """Test importing facts from dictionary format."""
        facts_data = [
            {
                'subject': 'cat',
                'predicate': 'is',
                'object': 'animal',
                'confidence': 0.9
            },
            {
                'subject': 'cat',
                'predicate': 'is',
                'object': 'animal',
                'negated': True,
                'confidence': 0.8
            }
        ]
        
        contradictions = self.detector.import_facts_from_dict(facts_data)
        
        self.assertEqual(len(contradictions), 1)
        self.assertEqual(len(self.detector.facts), 2)
        self.assertEqual(contradictions[0].contradiction_type, ContradictionType.DIRECT)

class TestContradiction(unittest.TestCase):
    """Test the Contradiction class."""
    
    def test_contradiction_creation(self):
        """Test basic contradiction creation."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        contradiction = Contradiction(
            fact1=fact1,
            fact2=fact2,
            contradiction_type=ContradictionType.DIRECT,
            severity=ContradictionSeverity.CRITICAL,
            explanation="Test contradiction",
            confidence=0.9
        )
        
        self.assertEqual(contradiction.fact1, fact1)
        self.assertEqual(contradiction.fact2, fact2)
        self.assertEqual(contradiction.contradiction_type, ContradictionType.DIRECT)
        self.assertEqual(contradiction.severity, ContradictionSeverity.CRITICAL)
        self.assertIsNotNone(contradiction.timestamp)
    
    def test_contradiction_string_representation(self):
        """Test string representation of contradictions."""
        fact1 = Fact("cat", "is", "animal")
        fact2 = Fact("cat", "is", "animal", negated=True)
        
        contradiction = Contradiction(
            fact1=fact1,
            fact2=fact2,
            contradiction_type=ContradictionType.DIRECT,
            severity=ContradictionSeverity.CRITICAL,
            explanation="Test contradiction",
            confidence=0.9
        )
        
        str_repr = str(contradiction)
        self.assertIn("DIRECT", str_repr)
        self.assertIn("cat is animal", str_repr)
        self.assertIn("cat not is animal", str_repr)

class InteractiveMode:
    """Interactive testing mode for manual verification."""
    
    def __init__(self):
        self.detector = ContradictionDetector()
    
    def run(self):
        """Run interactive contradiction detection."""
        print("\n🔍 Contradiction Detection Interactive Mode")
        print("Enter facts in format: subject predicate object [negated]")
        print("Commands: 'stats', 'contradictions', 'clear', 'quit'")
        print("Example: cat is animal")
        print("Example: cat is animal negated")
        
        while True:
            try:
                user_input = input("\n> ").strip().lower()
                
                if user_input == 'quit':
                    break
                elif user_input == 'stats':
                    self._show_stats()
                elif user_input == 'contradictions':
                    self._show_contradictions()
                elif user_input == 'clear':
                    self.detector.clear_all()
                    print("✅ Cleared all facts and contradictions")
                elif user_input:
                    self._process_fact(user_input)
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def _process_fact(self, fact_str: str):
        """Process a fact string."""
        parts = fact_str.split()
        if len(parts) < 3:
            print("❌ Invalid format. Use: subject predicate object [negated]")
            return
        
        subject, predicate, obj = parts[0], parts[1], parts[2]
        negated = len(parts) > 3 and parts[3] == 'negated'
        
        fact = Fact(subject, predicate, obj, negated=negated)
        contradictions = self.detector.add_fact(fact)
        
        print(f"✅ Added: {fact}")
        
        if contradictions:
            print(f"⚠️  Found {len(contradictions)} contradiction(s):")
            for contradiction in contradictions:
                print(f"   - {contradiction.explanation}")
        else:
            print("✅ No contradictions detected")
    
    def _show_stats(self):
        """Show detection statistics."""
        stats = self.detector.get_statistics()
        print("\n📊 Statistics:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
    
    def _show_contradictions(self):
        """Show all contradictions."""
        contradictions = self.detector.contradictions
        if not contradictions:
            print("✅ No contradictions found")
            return
        
        print(f"\n⚠️  Found {len(contradictions)} contradiction(s):")
        for i, contradiction in enumerate(contradictions, 1):
            print(f"   {i}. {contradiction}")
            print(f"      {contradiction.explanation}")
            print(f"      Severity: {contradiction.severity.value}")
            print(f"      Confidence: {contradiction.confidence:.2f}")

def run_all_tests():
    """Run all automated tests."""
    print("\n🧪 Running Contradiction Detection Tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [TestFact, TestContradictionDetector, TestContradiction]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    success_rate = ((total_tests - failures - errors) / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n📊 Test Results:")
    print(f"   Total tests: {total_tests}")
    print(f"   Passed: {total_tests - failures - errors}")
    print(f"   Failed: {failures}")
    print(f"   Errors: {errors}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    if failures > 0:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError: ')[-1].split('\n')[0]}")
    
    if errors > 0:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('\n')[-2]}")
    
    return success_rate >= 95.0

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive = InteractiveMode()
        interactive.run()
    else:
        success = run_all_tests()
        sys.exit(0 if success else 1)