#!/usr/bin/env python3
"""
Hierarchy-Aware Knowledge Base for the Human-Like Language Engine V3
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple, Set
from config import config
from logger import logger, KnowledgeError
from hierarchy_manager import HierarchyManager

class HierarchyAwareKnowledgeBase:
    """Enhanced knowledge base with semantic hierarchy support"""
    
    def __init__(self, kb_file: Optional[str] = None, hierarchy_file: Optional[str] = None):
        self.kb_file = kb_file or config.get('knowledge_base.file_path', 'knowledge_base.json')
        self.hierarchy_manager = HierarchyManager(hierarchy_file or 'hierarchy_schema.json')
        
        self.knowledge: Dict[str, Any] = {}
        self.learned_facts: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        
        self.load_knowledge_base()
        self._validate_and_upgrade_concepts()
    
    def load_knowledge_base(self) -> None:
        """Load knowledge base from file"""
        try:
            if os.path.exists(self.kb_file):
                with open(self.kb_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.knowledge = data.get('concepts', {})
                self.learned_facts = data.get('learned_facts', {})
                self.metadata = data.get('metadata', {})
                
                logger.info(f"Loaded hierarchy-aware knowledge base with {len(self.knowledge)} concepts", 
                          file=self.kb_file)
            else:
                logger.warning(f"Knowledge base file not found: {self.kb_file}")
                self._create_empty_kb()
                
        except (json.JSONDecodeError, FileNotFoundError) as e:
            logger.error(f"Failed to load knowledge base", exception=e)
            raise KnowledgeError(f"Cannot load knowledge base: {e}")
    
    def _validate_and_upgrade_concepts(self) -> None:
        """Validate existing concepts against hierarchy and upgrade if needed"""
        upgraded_count = 0
        
        for concept_name, concept_data in self.knowledge.items():
            concept_type = concept_data.get('type', 'unknown')
            
            # Validate type against hierarchy
            if concept_type != 'unknown' and not self.hierarchy_manager.is_valid_type(concept_type):
                logger.warning(f"Invalid type '{concept_type}' for concept '{concept_name}', setting to 'unknown'")
                concept_data['type'] = 'unknown'
                upgraded_count += 1
            
            # Add hierarchy_path if missing
            if 'hierarchy_path' not in concept_data and concept_type != 'unknown':
                concept_data['hierarchy_path'] = self.hierarchy_manager.get_type_path(concept_type)
                upgraded_count += 1
            
            # Add inherited properties and actions if missing
            if 'inherited_properties' not in concept_data and concept_type != 'unknown':
                concept_data['inherited_properties'] = self.hierarchy_manager.get_inherited_properties(concept_type)
                upgraded_count += 1
            
            if 'inherited_actions' not in concept_data and concept_type != 'unknown':
                concept_data['inherited_actions'] = self.hierarchy_manager.get_inherited_actions(concept_type)
                upgraded_count += 1
        
        if upgraded_count > 0:
            logger.info(f"Upgraded {upgraded_count} concept fields with hierarchy data")
            if config.get('knowledge_base.auto_save', True):
                self.save_knowledge_base()
    
    def save_knowledge_base(self) -> None:
        """Save knowledge base to file with backup"""
        try:
            # Create backup if enabled
            if config.get('knowledge_base.backup_enabled', True) and os.path.exists(self.kb_file):
                backup_file = f"{self.kb_file}.backup"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                os.rename(self.kb_file, backup_file)
                logger.debug(f"Created backup: {backup_file}")
            
            # Update metadata
            self.metadata.update({
                'last_modified': datetime.now().isoformat(),
                'total_concepts': len(self.knowledge),
                'learned_facts_count': len(self.learned_facts),
                'hierarchy_enabled': True,
                'hierarchy_version': '1.0'
            })
            
            # Save to file
            data = {
                'concepts': self.knowledge,
                'learned_facts': self.learned_facts,
                'metadata': self.metadata
            }
            
            with open(self.kb_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved hierarchy-aware knowledge base", concepts=len(self.knowledge))
            
        except Exception as e:
            logger.error(f"Failed to save knowledge base", exception=e)
            raise KnowledgeError(f"Cannot save knowledge base: {e}")
    
    def _create_empty_kb(self) -> None:
        """Create an empty knowledge base structure"""
        self.knowledge = {}
        self.learned_facts = {}
        self.metadata = {
            'version': '3.0',
            'created': datetime.now().isoformat(),
            'total_concepts': 0,
            'hierarchy_enabled': True
        }
        logger.info("Created empty hierarchy-aware knowledge base")
    
    def get_concept(self, concept: str) -> Optional[Dict[str, Any]]:
        """Get concept information with hierarchy data"""
        concept_data = self.knowledge.get(concept.lower())
        if concept_data:
            # Ensure hierarchy data is up-to-date
            concept_type = concept_data.get('type', 'unknown')
            if concept_type != 'unknown':
                concept_data['hierarchy_path'] = self.hierarchy_manager.get_type_path(concept_type)
                concept_data['inherited_properties'] = self.hierarchy_manager.get_inherited_properties(concept_type)
                concept_data['inherited_actions'] = self.hierarchy_manager.get_inherited_actions(concept_type)
        return concept_data
    
    def has_concept(self, concept: str) -> bool:
        """Check if concept exists"""
        return concept.lower() in self.knowledge
    
    def add_concept(self, concept: str, concept_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add new concept to knowledge base with hierarchy validation"""
        concept_key = concept.lower()
        
        if concept_key in self.knowledge:
            logger.warning(f"Concept already exists: {concept}")
            return {
                'success': False,
                'message': f"Concept '{concept}' already exists",
                'concept': concept
            }
        
        # Validate concept data structure
        required_fields = ['type']
        for field in required_fields:
            if field not in concept_data:
                raise KnowledgeError(f"Missing required field '{field}' for concept '{concept}'")
        
        # Validate type against hierarchy
        concept_type = concept_data['type']
        if concept_type != 'unknown' and not self.hierarchy_manager.is_valid_type(concept_type):
            raise KnowledgeError(f"Invalid type '{concept_type}' not found in hierarchy")
        
        # Add hierarchy information
        if concept_type != 'unknown':
            concept_data['hierarchy_path'] = self.hierarchy_manager.get_type_path(concept_type)
            concept_data['inherited_properties'] = self.hierarchy_manager.get_inherited_properties(concept_type)
            concept_data['inherited_actions'] = self.hierarchy_manager.get_inherited_actions(concept_type)
        
        self.knowledge[concept_key] = concept_data
        logger.info(f"Added new concept: {concept}", type=concept_data['type'])
        
        if config.get('knowledge_base.auto_save', True):
            self.save_knowledge_base()
        
        return {
            'success': True,
            'message': f"Successfully added concept '{concept}'",
            'concept': concept,
            'type': concept_type,
            'hierarchy_path': concept_data.get('hierarchy_path', []),
            'inherited_properties': concept_data.get('inherited_properties', []),
            'inherited_actions': concept_data.get('inherited_actions', [])
        }
    
    def update_concept_type(self, concept: str, new_type: str) -> Dict[str, Any]:
        """Update concept type with hierarchy validation and inheritance update"""
        concept_key = concept.lower()
        
        if concept_key not in self.knowledge:
            raise KnowledgeError(f"Concept not found: {concept}")
        
        # Validate new type
        if new_type != 'unknown' and not self.hierarchy_manager.is_valid_type(new_type):
            raise KnowledgeError(f"Invalid type '{new_type}' not found in hierarchy")
        
        concept_data = self.knowledge[concept_key]
        old_type = concept_data.get('type', 'unknown')
        
        # Update type and hierarchy information
        concept_data['type'] = new_type
        
        if new_type != 'unknown':
            concept_data['hierarchy_path'] = self.hierarchy_manager.get_type_path(new_type)
            concept_data['inherited_properties'] = self.hierarchy_manager.get_inherited_properties(new_type)
            concept_data['inherited_actions'] = self.hierarchy_manager.get_inherited_actions(new_type)
        else:
            concept_data.pop('hierarchy_path', None)
            concept_data.pop('inherited_properties', None)
            concept_data.pop('inherited_actions', None)
        
        logger.info(f"Updated concept type: {concept} from {old_type} to {new_type}")
        
        if config.get('knowledge_base.auto_save', True):
            self.save_knowledge_base()
        
        return {
            'concept': concept,
            'old_type': old_type,
            'new_type': new_type,
            'hierarchy_path': concept_data.get('hierarchy_path', []),
            'inherited_properties': concept_data.get('inherited_properties', []),
            'inherited_actions': concept_data.get('inherited_actions', [])
        }
    
    def get_all_properties_for_subject(self, subject: str) -> Dict[str, List[str]]:
        """Get both direct and inherited properties for a subject"""
        concept = self.get_concept(subject)
        if not concept:
            return {'direct': [], 'inherited': []}
        
        direct_properties = concept.get('properties', [])
        inherited_properties = concept.get('inherited_properties', [])
        
        return {
            'direct': direct_properties,
            'inherited': inherited_properties,
            'all': list(set(direct_properties + inherited_properties))
        }
    
    def get_all_actions_for_subject(self, subject: str) -> Dict[str, List[str]]:
        """Get both direct and inherited actions for a subject"""
        concept = self.get_concept(subject)
        if not concept:
            return {'direct': [], 'inherited': []}
        
        direct_actions = concept.get('actions', [])
        inherited_actions = concept.get('inherited_actions', [])
        
        return {
            'direct': direct_actions,
            'inherited': inherited_actions,
            'all': list(set(direct_actions + inherited_actions))
        }
    
    def can_perform_action(self, subject: str, action: str) -> Dict[str, Any]:
        """Check if subject can perform action through direct or inherited capabilities"""
        actions_data = self.get_all_actions_for_subject(subject)
        
        if action in actions_data['all']:
            source = 'direct' if action in actions_data['direct'] else 'inherited'
            return {
                'can_perform': True,
                'source': source,
                'reason': f"{subject} can {action} ({source} capability)"
            }
        
        return {
            'can_perform': False,
            'source': None,
            'reason': f"{subject} cannot {action}",
            'suggestions': self._get_action_suggestions(action)
        }
    
    def _get_action_suggestions(self, action: str) -> List[str]:
        """Get suggestions for entities that can perform the action"""
        suggestions = []
        
        for concept_name, concept_data in self.knowledge.items():
            all_actions = concept_data.get('actions', []) + concept_data.get('inherited_actions', [])
            if action in all_actions:
                suggestions.append(concept_name)
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    def find_concepts_by_hierarchy(self, hierarchy_type: str, include_subtypes: bool = True) -> List[str]:
        """Find all concepts of a specific hierarchy type"""
        matching_concepts = []
        
        for concept_name, concept_data in self.knowledge.items():
            concept_type = concept_data.get('type', 'unknown')
            
            if concept_type == hierarchy_type:
                matching_concepts.append(concept_name)
            elif include_subtypes and self.hierarchy_manager.is_subtype_of(concept_type, hierarchy_type):
                matching_concepts.append(concept_name)
        
        return matching_concepts
    
    def get_concept_relationships(self, concept: str) -> Dict[str, Any]:
        """Get hierarchical relationships for a concept"""
        concept_data = self.get_concept(concept)
        if not concept_data:
            return {}
        
        concept_type = concept_data.get('type', 'unknown')
        if concept_type == 'unknown':
            return {'type': 'unknown', 'relationships': {}}
        
        return {
            'type': concept_type,
            'hierarchy_path': self.hierarchy_manager.get_type_path(concept_type),
            'parent': self.hierarchy_manager.get_parent(concept_type),
            'children': self.hierarchy_manager.get_children(concept_type),
            'ancestors': self.hierarchy_manager.get_ancestors(concept_type),
            'descendants': self.hierarchy_manager.get_descendants(concept_type),
            'level': self.hierarchy_manager.get_type_level(concept_type)
        }
    
    def learn_fact_with_hierarchy(self, subject: str, fact_type: str, fact_value: str) -> Dict[str, Any]:
        """Learn new fact with hierarchy validation and inheritance consideration"""
        subject_key = subject.lower()
        
        # Initialize subject if not exists
        if subject_key not in self.knowledge:
            # Try to infer type from fact_value if it's a type fact
            inferred_type = 'unknown'
            if fact_type == 'type' and self.hierarchy_manager.is_valid_type(fact_value):
                inferred_type = fact_value
            
            self.knowledge[subject_key] = {
                'type': inferred_type,
                'actions': [],
                'properties': [],
                'meaning': f'learned concept: {subject}'
            }
            
            if inferred_type != 'unknown':
                self.knowledge[subject_key]['hierarchy_path'] = self.hierarchy_manager.get_type_path(inferred_type)
                self.knowledge[subject_key]['inherited_properties'] = self.hierarchy_manager.get_inherited_properties(inferred_type)
                self.knowledge[subject_key]['inherited_actions'] = self.hierarchy_manager.get_inherited_actions(inferred_type)
        
        subject_data = self.knowledge[subject_key]
        result = {'success': False, 'message': '', 'changes': []}
        
        if fact_type == 'action':
            # Check if action is already inherited
            inherited_actions = subject_data.get('inherited_actions', [])
            if fact_value in inherited_actions:
                result['message'] = f"{subject} can already {fact_value} (inherited capability)"
                result['success'] = True
            else:
                if 'actions' not in subject_data:
                    subject_data['actions'] = []
                if fact_value not in subject_data['actions']:
                    subject_data['actions'].append(fact_value)
                    result['message'] = f"Learned: {subject} can {fact_value} (direct capability)"
                    result['changes'].append(f"Added action: {fact_value}")
                    result['success'] = True
                else:
                    result['message'] = f"Already know: {subject} can {fact_value} (direct capability)"
                    result['success'] = True
        
        elif fact_type == 'property':
            # Check if property is already inherited
            inherited_properties = subject_data.get('inherited_properties', [])
            if fact_value in inherited_properties:
                result['message'] = f"{subject} is already {fact_value} (inherited property)"
                result['success'] = True
            else:
                if 'properties' not in subject_data:
                    subject_data['properties'] = []
                if fact_value not in subject_data['properties']:
                    subject_data['properties'].append(fact_value)
                    result['message'] = f"Learned: {subject} is {fact_value} (direct property)"
                    result['changes'].append(f"Added property: {fact_value}")
                    result['success'] = True
                else:
                    result['message'] = f"Already know: {subject} is {fact_value} (direct property)"
                    result['success'] = True
        
        elif fact_type == 'type':
            if self.hierarchy_manager.is_valid_type(fact_value):
                type_update_result = self.update_concept_type(subject, fact_value)
                result['message'] = f"Updated {subject} type to {fact_value}"
                result['changes'].append(f"Type changed: {type_update_result['old_type']} → {fact_value}")
                result['hierarchy_info'] = type_update_result
                result['success'] = True
            else:
                result['message'] = f"Invalid type '{fact_value}' not found in hierarchy"
                result['success'] = False
        
        else:
            result['message'] = f"Unknown fact type: {fact_type}"
            result['success'] = False
        
        # Store in learned facts for tracking
        if result['success']:
            fact_key = f"{subject}_{fact_type}_{fact_value}"
            self.learned_facts[fact_key] = {
                'subject': subject,
                'fact_type': fact_type,
                'fact_value': fact_value,
                'learned_at': datetime.now().isoformat(),
                'hierarchy_aware': True
            }
            
            logger.log_learning_event(subject, fact_type, fact_value)
            
            if config.get('knowledge_base.auto_save', True):
                self.save_knowledge_base()
        
        return result
    
    def get_enhanced_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics including hierarchy information"""
        base_stats = self.get_statistics()
        
        # Hierarchy-specific statistics
        hierarchy_stats = self.hierarchy_manager.get_hierarchy_statistics()
        
        # Concept distribution by hierarchy level
        concepts_by_level = {}
        concepts_with_inheritance = 0
        
        for concept_data in self.knowledge.values():
            concept_type = concept_data.get('type', 'unknown')
            if concept_type != 'unknown':
                level = self.hierarchy_manager.get_type_level(concept_type)
                if level not in concepts_by_level:
                    concepts_by_level[level] = 0
                concepts_by_level[level] += 1
                
                if concept_data.get('inherited_properties') or concept_data.get('inherited_actions'):
                    concepts_with_inheritance += 1
        
        base_stats.update({
            'hierarchy_statistics': hierarchy_stats,
            'concepts_by_hierarchy_level': concepts_by_level,
            'concepts_with_inheritance': concepts_with_inheritance,
            'inheritance_coverage': f"{(concepts_with_inheritance / len(self.knowledge) * 100):.1f}%" if self.knowledge else "0%"
        })
        
        return base_stats
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get basic knowledge base statistics"""
        types_count = {}
        for concept_data in self.knowledge.values():
            concept_type = concept_data.get('type', 'unknown')
            types_count[concept_type] = types_count.get(concept_type, 0) + 1
        
        return {
            'total_concepts': len(self.knowledge),
            'learned_facts': len(self.learned_facts),
            'types_distribution': types_count,
            'last_modified': self.metadata.get('last_modified', 'unknown')
        }
    
    def export_hierarchy_report(self, filename: str) -> None:
        """Export detailed hierarchy report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'knowledge_base_stats': self.get_enhanced_statistics(),
                'hierarchy_tree': self.hierarchy_manager.export_hierarchy_tree(),
                'concepts_by_type': {},
                'inheritance_details': {}
            }
            
            # Group concepts by type
            for concept_name, concept_data in self.knowledge.items():
                concept_type = concept_data.get('type', 'unknown')
                if concept_type not in report['concepts_by_type']:
                    report['concepts_by_type'][concept_type] = []
                report['concepts_by_type'][concept_type].append(concept_name)
                
                # Add inheritance details
                if concept_type != 'unknown':
                    report['inheritance_details'][concept_name] = {
                        'type': concept_type,
                        'hierarchy_path': concept_data.get('hierarchy_path', []),
                        'inherited_properties': concept_data.get('inherited_properties', []),
                        'inherited_actions': concept_data.get('inherited_actions', []),
                        'direct_properties': concept_data.get('properties', []),
                        'direct_actions': concept_data.get('actions', [])
                    }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Exported hierarchy report", file=filename)
            
        except Exception as e:
            logger.error(f"Failed to export hierarchy report", exception=e)
            raise KnowledgeError(f"Cannot export hierarchy report: {e}")


# Example usage and testing
if __name__ == "__main__":
    try:
        print("🧠 Hierarchy-Aware Knowledge Base Test")
        print("=" * 40)
        
        # Initialize knowledge base
        kb = HierarchyAwareKnowledgeBase()
        
        # Test adding a concept with hierarchy
        print("\n📝 Adding concept 'tiger' as mammal:")
        kb.add_concept('tiger', {
            'type': 'mammal',
            'actions': ['hunt', 'roar'],
            'properties': ['striped', 'large'],
            'meaning': 'a large wild cat with stripes'
        })
        
        # Test getting concept with inheritance
        print("\n🔍 Getting tiger concept with inheritance:")
        tiger = kb.get_concept('tiger')
        if tiger:
            print(f"  Type: {tiger['type']}")
            print(f"  Hierarchy Path: {tiger.get('hierarchy_path', [])}")
            print(f"  Direct Actions: {tiger.get('actions', [])}")
            print(f"  Inherited Actions: {tiger.get('inherited_actions', [])}")
            print(f"  Direct Properties: {tiger.get('properties', [])}")
            print(f"  Inherited Properties: {tiger.get('inherited_properties', [])}")
        
        # Test action capability check
        print("\n✅ Testing action capabilities:")
        can_move = kb.can_perform_action('tiger', 'move')
        print(f"  Can tiger move? {can_move}")
        
        can_fly = kb.can_perform_action('tiger', 'fly')
        print(f"  Can tiger fly? {can_fly}")
        
        # Test hierarchy queries
        print("\n🌳 Hierarchy queries:")
        animals = kb.find_concepts_by_hierarchy('animal', include_subtypes=True)
        print(f"  All animals: {animals}")
        
        mammals = kb.find_concepts_by_hierarchy('mammal', include_subtypes=False)
        print(f"  Only mammals: {mammals}")
        
        # Test relationships
        print("\n🔗 Tiger relationships:")
        relationships = kb.get_concept_relationships('tiger')
        for key, value in relationships.items():
            print(f"  {key}: {value}")
        
        # Test enhanced statistics
        print("\n📊 Enhanced Statistics:")
        stats = kb.get_enhanced_statistics()
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ Error: {e}")