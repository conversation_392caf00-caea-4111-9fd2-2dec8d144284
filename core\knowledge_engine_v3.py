#!/usr/bin/env python3
"""
Hierarchy-Aware Human-Like Language Engine V3
Integrates semantic hierarchy and inheritance for enhanced understanding
"""

import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .knowledge_base_v3 import HierarchyAwareKnowledgeBase
from .hierarchy_manager import HierarchyManager
from .parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QuestionParser
from .validator import Semantic<PERSON>ali<PERSON><PERSON>
from .logger import logger, EngineError
from config.config import config

class HierarchyAwareKnowledgeEngine:
    """Enhanced knowledge engine with semantic hierarchy support"""
    
    def __init__(self, kb_file: Optional[str] = None, hierarchy_file: Optional[str] = None):
        """Initialize the hierarchy-aware knowledge engine"""
        logger.info("Initializing Hierarchy-Aware Knowledge Engine V3")
        
        # Initialize components
        self.knowledge_base = HierarchyAwareKnowledgeBase(kb_file, hierarchy_file)
        self.hierarchy_manager = self.knowledge_base.hierarchy_manager
        self.sentence_parser = SentenceParser()
        self.question_parser = QuestionParser()
        self.validator = SemanticValidator(self.knowledge_base)
        
        # Performance tracking
        self.performance_stats = {
            'sentences_processed': 0,
            'questions_answered': 0,
            'facts_learned': 0,
            'validation_errors': 0,
            'start_time': datetime.now()
        }
        
        logger.info("Engine initialized successfully", 
                   concepts=len(self.knowledge_base.knowledge),
                   hierarchy_types=len(self.hierarchy_manager.hierarchy))
    
    def validate_sentence(self, sentence: str) -> Dict[str, Any]:
        """Validate sentence with hierarchy-aware semantic checking"""
        start_time = time.time()
        
        try:
            # Parse sentence
            parse_result = self.sentence_parser.parse(sentence)
            
            # The parser returns the parsed result directly or raises an exception
            # No need to check for 'success' key
            
            # Semantic validation with hierarchy awareness
            validation_result = self.validator.validate_with_hierarchy(
                parse_result['subject'],
                parse_result['verb'],
                parse_result.get('object')
            )
            
            self.performance_stats['sentences_processed'] += 1
            
            if not validation_result['valid']:
                self.performance_stats['validation_errors'] += 1
            
            validation_result['processing_time'] = time.time() - start_time
            validation_result['parse_info'] = parse_result
            
            logger.debug(f"Sentence validation completed", 
                        sentence=sentence, 
                        valid=validation_result['valid'],
                        time=f"{validation_result['processing_time']:.3f}s")
            
            return validation_result
            
        except Exception as e:
            self.performance_stats['validation_errors'] += 1
            logger.error(f"Validation failed", sentence=sentence, exception=e)
            return {
                'valid': False,
                'error': f"Validation error: {str(e)}",
                'processing_time': time.time() - start_time
            }
    
    def answer_question(self, question: str) -> Dict[str, Any]:
        """Answer questions with hierarchy-aware responses"""
        start_time = time.time()
        
        try:
            # Parse question
            question_result = self.question_parser.parse_question(question)
            if not question_result:
                return {
                    'success': False,
                    'answer': "I don't understand that question format.",
                    'error': "No matching question pattern found",
                    'processing_time': time.time() - start_time
                }
            
            # Generate hierarchy-aware answer
            answer_result = self._generate_hierarchy_aware_answer(
                question_result['type'],
                question_result['subject'],
                question_result.get('context', {})
            )
            
            self.performance_stats['questions_answered'] += 1
            answer_result['processing_time'] = time.time() - start_time
            answer_result['question_info'] = question_result
            
            logger.debug(f"Question answered", 
                        question=question,
                        type=question_result['type'],
                        time=f"{answer_result['processing_time']:.3f}s")
            
            return answer_result
            
        except Exception as e:
            logger.error(f"Question answering failed", question=question, exception=e)
            return {
                'success': False,
                'answer': f"Error processing question: {str(e)}",
                'processing_time': time.time() - start_time
            }
    
    def _generate_hierarchy_aware_answer(self, question_type: str, subject: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate answers using hierarchy information"""
        subject_lower = subject.lower()
        
        if question_type == "what_can_do":
            return self._answer_what_can_do(subject_lower)
        elif question_type == "what_is":
            return self._answer_what_is(subject_lower)
        elif question_type == "who_is":
            return self._answer_who_is(subject_lower)
        elif question_type == "where_is":
            return self._answer_where_is(subject_lower)
        elif question_type == "how_many":
            return self._answer_how_many(subject_lower, context)
        elif question_type == "what_type":
            return self._answer_what_type(subject_lower)
        else:
            return {
                'success': False,
                'answer': f"I don't know how to answer '{question_type}' questions yet."
            }
    
    def _answer_what_can_do(self, subject: str) -> Dict[str, Any]:
        """Answer 'what can X do?' with hierarchy-aware capabilities"""
        actions_data = self.knowledge_base.get_all_actions_for_subject(subject)
        
        if not actions_data['all']:
            # Try to find similar concepts
            suggestions = self._find_similar_concepts(subject)
            return {
                'success': False,
                'answer': f"I don't know what {subject} can do.",
                'suggestions': suggestions,
                'hierarchy_info': self.knowledge_base.get_concept_relationships(subject)
            }
        
        # Build comprehensive answer
        answer_parts = []
        
        if actions_data['direct']:
            answer_parts.append(f"{subject} can {', '.join(actions_data['direct'])} (direct capabilities)")
        
        if actions_data['inherited']:
            answer_parts.append(f"{subject} can also {', '.join(actions_data['inherited'])} (inherited capabilities)")
        
        return {
            'success': True,
            'answer': '. '.join(answer_parts) + '.',
            'capabilities': actions_data,
            'hierarchy_info': self.knowledge_base.get_concept_relationships(subject)
        }
    
    def _answer_what_is(self, subject: str) -> Dict[str, Any]:
        """Answer 'what is X?' with hierarchy context"""
        concept = self.knowledge_base.get_concept(subject)
        
        if not concept:
            suggestions = self._find_similar_concepts(subject)
            return {
                'success': False,
                'answer': f"I don't know what {subject} is.",
                'suggestions': suggestions
            }
        
        # Build hierarchical description
        answer_parts = []
        
        # Basic meaning
        if 'meaning' in concept:
            answer_parts.append(concept['meaning'])
        
        # Type and hierarchy information
        concept_type = concept.get('type', 'unknown')
        if concept_type != 'unknown':
            hierarchy_path = concept.get('hierarchy_path', [])
            if len(hierarchy_path) > 1:
                answer_parts.append(f"{subject} is a type of {' → '.join(hierarchy_path[:-1])}")
            else:
                answer_parts.append(f"{subject} is a {concept_type}")
        
        # Properties (direct and inherited)
        properties_data = self.knowledge_base.get_all_properties_for_subject(subject)
        if properties_data['all']:
            if properties_data['direct'] and properties_data['inherited']:
                answer_parts.append(f"{subject} is {', '.join(properties_data['direct'])} (direct) and {', '.join(properties_data['inherited'])} (inherited)")
            elif properties_data['all']:
                source = 'direct' if properties_data['direct'] else 'inherited'
                answer_parts.append(f"{subject} is {', '.join(properties_data['all'])} ({source})")
        
        return {
            'success': True,
            'answer': '. '.join(answer_parts) + '.',
            'concept_info': concept,
            'properties': properties_data,
            'hierarchy_info': self.knowledge_base.get_concept_relationships(subject)
        }
    
    def _answer_who_is(self, subject: str) -> Dict[str, Any]:
        """Answer 'who is X?' focusing on agent-type entities"""
        concept = self.knowledge_base.get_concept(subject)
        
        if not concept:
            return {
                'success': False,
                'answer': f"I don't know who {subject} is.",
                'suggestions': self._find_similar_concepts(subject)
            }
        
        concept_type = concept.get('type', 'unknown')
        
        # Check if it's an agent-type entity
        if self.hierarchy_manager.is_subtype_of(concept_type, 'agent'):
            answer = f"{subject} is {concept.get('meaning', 'an agent')}"
            
            # Add hierarchy context for agents
            hierarchy_path = concept.get('hierarchy_path', [])
            if len(hierarchy_path) > 1:
                answer += f" ({subject} is a type of {' → '.join(hierarchy_path[:-1])})"
            
            return {
                'success': True,
                'answer': answer + '.',
                'agent_info': concept,
                'hierarchy_info': self.knowledge_base.get_concept_relationships(subject)
            }
        else:
            return {
                'success': False,
                'answer': f"{subject} is not a person or agent, it's a {concept_type}.",
                'concept_info': concept
            }
    
    def _answer_where_is(self, subject: str) -> Dict[str, Any]:
        """Answer 'where is X?' focusing on location information"""
        concept = self.knowledge_base.get_concept(subject)
        
        if not concept:
            return {
                'success': False,
                'answer': f"I don't know where {subject} is.",
                'suggestions': self._find_similar_concepts(subject)
            }
        
        concept_type = concept.get('type', 'unknown')
        
        # Check if it's a location
        if concept_type == 'location' or self.hierarchy_manager.is_subtype_of(concept_type, 'location'):
            return {
                'success': True,
                'answer': f"{subject} is {concept.get('meaning', 'a location')}.",
                'location_info': concept
            }
        
        # Look for location-related properties
        properties = concept.get('properties', []) + concept.get('inherited_properties', [])
        location_properties = [prop for prop in properties if 'location' in prop.lower() or 'place' in prop.lower()]
        
        if location_properties:
            return {
                'success': True,
                'answer': f"{subject} is found in {', '.join(location_properties)}.",
                'location_properties': location_properties
            }
        
        return {
            'success': False,
            'answer': f"I don't have location information for {subject}.",
            'concept_info': concept
        }
    
    def _answer_how_many(self, subject: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Answer 'how many X?' questions using hierarchy statistics"""
        # Count concepts of the specified type
        matching_concepts = self.knowledge_base.find_concepts_by_hierarchy(subject, include_subtypes=True)
        
        if not matching_concepts:
            return {
                'success': False,
                'answer': f"I don't know of any {subject} in my knowledge base."
            }
        
        count = len(matching_concepts)
        answer = f"I know of {count} {subject}"
        
        if count > 1:
            answer += f": {', '.join(matching_concepts[:5])}"
            if count > 5:
                answer += f" and {count - 5} more"
        else:
            answer += f": {matching_concepts[0]}"
        
        return {
            'success': True,
            'answer': answer + '.',
            'count': count,
            'examples': matching_concepts[:10]
        }
    
    def _answer_what_type(self, subject: str) -> Dict[str, Any]:
        """Answer 'what type is X?' questions"""
        concept = self.knowledge_base.get_concept(subject)
        
        if not concept:
            return {
                'success': False,
                'answer': f"I don't know what type {subject} is.",
                'suggestions': self._find_similar_concepts(subject)
            }
        
        concept_type = concept.get('type', 'unknown')
        hierarchy_path = concept.get('hierarchy_path', [])
        
        if concept_type == 'unknown':
            return {
                'success': False,
                'answer': f"I don't know the specific type of {subject}."
            }
        
        answer = f"{subject} is a {concept_type}"
        
        if len(hierarchy_path) > 1:
            answer += f" (which is a type of {' → '.join(hierarchy_path[:-1])})"
        
        return {
            'success': True,
            'answer': answer + '.',
            'type_info': {
                'direct_type': concept_type,
                'hierarchy_path': hierarchy_path,
                'level': self.hierarchy_manager.get_type_level(concept_type)
            }
        }
    
    def _find_similar_concepts(self, target: str) -> List[str]:
        """Find concepts similar to the target"""
        suggestions = []
        target_lower = target.lower()
        
        for concept_name in self.knowledge_base.knowledge.keys():
            if target_lower in concept_name or concept_name in target_lower:
                suggestions.append(concept_name)
        
        return suggestions[:5]
    
    def learn_from_sentence(self, sentence: str) -> Dict[str, Any]:
        """Learn new facts from sentences with hierarchy awareness"""
        start_time = time.time()
        
        try:
            # First validate the sentence
            validation_result = self.validate_sentence(sentence)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': f"Cannot learn from invalid sentence: {validation_result['error']}",
                    'validation_result': validation_result,
                    'processing_time': time.time() - start_time
                }
            
            parse_info = validation_result['parse_info']
            subject = parse_info['subject']
            verb = parse_info['verb']
            obj = parse_info.get('object')
            
            # Determine fact type and value
            fact_type, fact_value = self._extract_fact_from_parse(verb, obj)
            
            if not fact_type:
                return {
                    'success': False,
                    'message': f"Cannot extract learnable fact from: {sentence}",
                    'processing_time': time.time() - start_time
                }
            
            # Learn with hierarchy awareness
            learning_result = self.knowledge_base.learn_fact_with_hierarchy(subject, fact_type, fact_value)
            
            self.performance_stats['facts_learned'] += 1
            learning_result['processing_time'] = time.time() - start_time
            learning_result['sentence'] = sentence
            
            logger.log_learning_event(subject, fact_type, fact_value)
            
            return learning_result
            
        except Exception as e:
            logger.error(f"Learning failed", sentence=sentence, exception=e)
            return {
                'success': False,
                'message': f"Learning error: {str(e)}",
                'processing_time': time.time() - start_time
            }
    
    def _extract_fact_from_parse(self, verb: str, obj: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
        """Extract fact type and value from parsed sentence components"""
        if verb == "can" and obj:
            return "action", obj
        elif verb == "is" and obj:
            # Check if object is a valid type in hierarchy
            if self.hierarchy_manager.is_valid_type(obj):
                return "type", obj
            else:
                return "property", obj
        elif verb in ["has", "have"] and obj:
            return "property", obj
        
        return None, None
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get comprehensive engine status including hierarchy information"""
        runtime = datetime.now() - self.performance_stats['start_time']
        
        return {
            'engine_version': '3.0 (Hierarchy-Aware)',
            'runtime': str(runtime),
            'performance': self.performance_stats.copy(),
            'knowledge_base': self.knowledge_base.get_enhanced_statistics(),
            'hierarchy': self.hierarchy_manager.get_hierarchy_statistics(),
            'components': {
                'knowledge_base': 'HierarchyAwareKnowledgeBase',
                'hierarchy_manager': 'HierarchyManager',
                'sentence_parser': 'SentenceParser',
                'question_parser': 'QuestionParser',
                'validator': 'SemanticValidator'
            }
        }
    
    def interactive_mode(self) -> None:
        """Run interactive mode for testing"""
        print("🧠 Hierarchy-Aware Knowledge Engine V3 - Interactive Mode")
        print("=" * 60)
        print("Commands:")
        print("  - Ask questions: 'what can cat do?', 'what is dog?', 'who is human?'")
        print("  - Learn facts: 'cat can jump', 'dog is mammal', 'bird can fly'")
        print("  - Check hierarchy: 'hierarchy cat', 'types animal'")
        print("  - Get status: 'status'")
        print("  - Exit: 'quit' or 'exit'")
        print("=" * 60)
        
        while True:
            try:
                user_input = input("\n> ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'status':
                    status = self.get_engine_status()
                    print("\n📊 Engine Status:")
                    for key, value in status.items():
                        if isinstance(value, dict):
                            print(f"  {key}:")
                            for sub_key, sub_value in value.items():
                                print(f"    {sub_key}: {sub_value}")
                        else:
                            print(f"  {key}: {value}")
                    continue
                
                if user_input.lower().startswith('hierarchy '):
                    concept = user_input[10:].strip()
                    relationships = self.knowledge_base.get_concept_relationships(concept)
                    print(f"\n🌳 Hierarchy for '{concept}':")
                    for key, value in relationships.items():
                        print(f"  {key}: {value}")
                    continue
                
                if user_input.lower().startswith('types '):
                    type_name = user_input[6:].strip()
                    concepts = self.knowledge_base.find_concepts_by_hierarchy(type_name, include_subtypes=True)
                    print(f"\n📋 Concepts of type '{type_name}': {concepts}")
                    continue
                
                # Try to answer as question first
                if '?' in user_input:
                    result = self.answer_question(user_input)
                    if result['success']:
                        print(f"\n💡 {result['answer']}")
                        if 'hierarchy_info' in result and result['hierarchy_info']:
                            print(f"   📍 Hierarchy: {result['hierarchy_info'].get('hierarchy_path', [])}")
                    else:
                        print(f"\n❓ {result['answer']}")
                        if 'suggestions' in result and result['suggestions']:
                            print(f"   💭 Did you mean: {', '.join(result['suggestions'])}?")
                else:
                    # Try to learn from statement
                    result = self.learn_from_sentence(user_input)
                    if result['success']:
                        print(f"\n✅ {result['message']}")
                        if 'hierarchy_info' in result and result['hierarchy_info']:
                            print(f"   📍 Hierarchy: {result['hierarchy_info'].get('hierarchy_path', [])}")
                    else:
                        print(f"\n❌ {result['message']}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n💥 Error: {e}")


# Example usage and testing
if __name__ == "__main__":
    try:
        print("🧠 Hierarchy-Aware Knowledge Engine V3 Test")
        print("=" * 50)
        
        # Initialize engine
        engine = HierarchyAwareKnowledgeEngine()
        
        # Test sentences
        test_sentences = [
            "cat can jump",
            "dog is mammal",
            "bird can fly",
            "human is agent"
        ]
        
        print("\n📝 Testing sentence validation and learning:")
        for sentence in test_sentences:
            print(f"\n  Sentence: '{sentence}'")
            
            # Validate
            validation = engine.validate_sentence(sentence)
            print(f"    Valid: {validation['valid']}")
            
            if validation['valid']:
                # Learn
                learning = engine.learn_from_sentence(sentence)
                print(f"    Learned: {learning['success']} - {learning['message']}")
        
        # Test questions
        test_questions = [
            "what can cat do?",
            "what is dog?",
            "who is human?",
            "what type is bird?",
            "how many mammal?"
        ]
        
        print("\n❓ Testing question answering:")
        for question in test_questions:
            print(f"\n  Q: {question}")
            result = engine.answer_question(question)
            print(f"  A: {result['answer']}")
            if 'hierarchy_info' in result and result['hierarchy_info']:
                hierarchy_path = result['hierarchy_info'].get('hierarchy_path', [])
                if hierarchy_path:
                    print(f"     📍 Hierarchy: {' → '.join(hierarchy_path)}")
        
        # Show engine status
        print("\n📊 Engine Status:")
        status = engine.get_engine_status()
        for key, value in status.items():
            if isinstance(value, dict) and key in ['performance', 'knowledge_base']:
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    if not isinstance(sub_value, dict):
                        print(f"    {sub_key}: {sub_value}")
            elif not isinstance(value, dict):
                print(f"  {key}: {value}")
        
        print("\n🎯 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()