#!/usr/bin/env python3

print("🧪 SIMPLE COMPONENT TEST")
print("=" * 50)

# Test Enhanced Parser
try:
    from core.enhanced_parser import EnhancedSentenceParser
    ep = EnhancedSentenceParser()
    result = ep.parse("Cats are mammals")
    print(f"✅ Enhanced Parser: {result.sentence_type}")
except Exception as e:
    print(f"❌ Enhanced Parser: {e}")

# Test Dependency Parser
try:
    from core.dependency_parser import DependencyParser
    dp = DependencyParser()
    result = dp.parse("Cats are mammals")
    print(f"✅ Dependency Parser: {len(result.tokens)} tokens")
except Exception as e:
    print(f"❌ Dependency Parser: {e}")

# Test Context Memory
try:
    from core.context_memory import ContextMemory, ContextType
    cm = ContextMemory()
    entry = cm.add_entry("Test sentence", ContextType.STATEMENT)
    print(f"✅ Context Memory: Entry added at {entry.timestamp}")
except Exception as e:
    print(f"❌ Context Memory: {e}")

# Test Conditional Logic
try:
    from core.conditional_logic import ConditionalLogicEngine
    cl = ConditionalLogicEngine()
    cl.add_fact("cats are mammals")
    print(f"✅ Conditional Logic: Facts added")
except Exception as e:
    print(f"❌ Conditional Logic: {e}")

print("\n🎯 Test Complete!")