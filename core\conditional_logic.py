#!/usr/bin/env python3
"""
Conditional Logic Engine for Knowledge Base

This module implements conditional reasoning capabilities including:
- If-then rule processing
- Inference chain execution
- Logical implication handling
- Rule validation and consistency checking

Author: AI Assistant
Date: 2024
"""

import time
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum

try:
    from .logger import EngineLogger
except ImportError:
    # Fallback for testing
    class EngineLogger:
        def debug(self, msg): print(f"DEBUG: {msg}")
        def info(self, msg): print(f"INFO: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")

class RuleType(Enum):
    """Types of conditional rules"""
    IMPLICATION = "implication"  # if A then B
    BICONDITIONAL = "biconditional"  # A if and only if B
    CAUSAL = "causal"  # A causes B
    TEMPORAL = "temporal"  # when A then B
    CONDITIONAL = "conditional"  # if A then B (general)

class LogicalOperator(Enum):
    """Logical operators for compound conditions"""
    AND = "and"
    OR = "or"
    NOT = "not"
    IMPLIES = "implies"
    IFF = "iff"  # if and only if

@dataclass
class Condition:
    """Represents a logical condition"""
    subject: str
    predicate: str
    object: Optional[str] = None
    negated: bool = False
    confidence: float = 1.0
    
    def __str__(self) -> str:
        neg = "not " if self.negated else ""
        if self.object:
            return f"{neg}{self.subject} {self.predicate} {self.object}"
        return f"{neg}{self.subject} {self.predicate}"

@dataclass
class ConditionalRule:
    """Represents a conditional rule (if-then statement)"""
    rule_id: str
    rule_type: RuleType
    antecedent: List[Condition]  # if part (premises)
    consequent: List[Condition]  # then part (conclusions)
    operator: LogicalOperator = LogicalOperator.AND
    confidence: float = 1.0
    created_at: float = None
    source: str = "user"
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
    
    def __str__(self) -> str:
        ant_str = f" {self.operator.value} ".join(str(c) for c in self.antecedent)
        con_str = f" {self.operator.value} ".join(str(c) for c in self.consequent)
        return f"IF {ant_str} THEN {con_str}"

@dataclass
class InferenceResult:
    """Result of an inference operation"""
    conclusion: Condition
    rule_chain: List[str]  # IDs of rules used
    confidence: float
    steps: List[str]  # Human-readable inference steps
    success: bool = True
    error_message: Optional[str] = None

class ConditionalLogicEngine:
    """Engine for processing conditional logic and inference"""
    
    def __init__(self, logger: Optional[EngineLogger] = None):
        self.logger = logger or EngineLogger()
        self.rules: Dict[str, ConditionalRule] = {}
        self.facts: Set[str] = set()  # Known facts as strings
        self.inference_cache: Dict[str, InferenceResult] = {}
        self.max_inference_depth = 10
        self.min_confidence_threshold = 0.1
        
    def add_rule(self, rule: ConditionalRule) -> bool:
        """Add a conditional rule to the engine"""
        try:
            # Validate rule consistency
            if self._validate_rule(rule):
                self.rules[rule.rule_id] = rule
                self.logger.info(f"Added rule: {rule}")
                return True
            else:
                self.logger.warning(f"Rule validation failed: {rule}")
                return False
        except Exception as e:
            self.logger.error(f"Error adding rule: {e}")
            return False
    
    def add_fact(self, fact: str) -> None:
        """Add a known fact to the knowledge base"""
        self.facts.add(fact.lower().strip())
        self.logger.debug(f"Added fact: {fact}")
    
    def remove_rule(self, rule_id: str) -> bool:
        """Remove a rule from the engine"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            # Clear related cache entries
            self.inference_cache.clear()
            self.logger.info(f"Removed rule: {rule_id}")
            return True
        return False
    
    def infer(self, query: Condition, max_depth: Optional[int] = None) -> InferenceResult:
        """Attempt to infer whether a condition is true"""
        max_depth = max_depth or self.max_inference_depth
        query_str = str(query)
        
        # Check cache first
        if query_str in self.inference_cache:
            return self.inference_cache[query_str]
        
        # Check if it's a known fact
        if query_str.lower() in self.facts:
            result = InferenceResult(
                conclusion=query,
                rule_chain=[],
                confidence=1.0,
                steps=[f"Known fact: {query_str}"]
            )
            self.inference_cache[query_str] = result
            return result
        
        # Attempt inference through rules
        result = self._infer_recursive(query, max_depth, set(), [])
        self.inference_cache[query_str] = result
        return result
    
    def _infer_recursive(self, query: Condition, depth: int, 
                        visited_rules: Set[str], steps: List[str]) -> InferenceResult:
        """Recursive inference with cycle detection"""
        if depth <= 0:
            return InferenceResult(
                conclusion=query,
                rule_chain=[],
                confidence=0.0,
                steps=steps + ["Maximum inference depth reached"],
                success=False,
                error_message="Inference depth limit exceeded"
            )
        
        query_str = str(query)
        
        # Look for applicable rules
        for rule_id, rule in self.rules.items():
            if rule_id in visited_rules:
                continue  # Avoid cycles
            
            # Check if this rule can conclude our query
            if self._rule_concludes(rule, query):
                # Check if we can prove the antecedent
                antecedent_result = self._prove_antecedent(
                    rule, depth - 1, visited_rules | {rule_id}, 
                    steps + [f"Trying rule {rule_id}: {rule}"]
                )
                
                if antecedent_result.success:
                    confidence = min(antecedent_result.confidence, rule.confidence)
                    if confidence >= self.min_confidence_threshold:
                        return InferenceResult(
                            conclusion=query,
                            rule_chain=antecedent_result.rule_chain + [rule_id],
                            confidence=confidence,
                            steps=antecedent_result.steps + [
                                f"Applied rule {rule_id} → {query_str}"
                            ]
                        )
        
        # No applicable rules found
        return InferenceResult(
            conclusion=query,
            rule_chain=[],
            confidence=0.0,
            steps=steps + [f"Cannot prove: {query_str}"],
            success=False,
            error_message="No applicable rules found"
        )
    
    def _rule_concludes(self, rule: ConditionalRule, query: Condition) -> bool:
        """Check if a rule can conclude the given query"""
        for consequent in rule.consequent:
            if self._conditions_match(consequent, query):
                return True
        return False
    
    def _conditions_match(self, condition1: Condition, condition2: Condition) -> bool:
        """Check if two conditions match (considering negation)"""
        return (
            condition1.subject.lower() == condition2.subject.lower() and
            condition1.predicate.lower() == condition2.predicate.lower() and
            (condition1.object or "").lower() == (condition2.object or "").lower() and
            condition1.negated == condition2.negated
        )
    
    def _prove_antecedent(self, rule: ConditionalRule, depth: int, 
                         visited_rules: Set[str], steps: List[str]) -> InferenceResult:
        """Attempt to prove the antecedent of a rule"""
        if not rule.antecedent:
            return InferenceResult(
                conclusion=Condition("true", "is", "true"),
                rule_chain=[],
                confidence=1.0,
                steps=steps + ["Empty antecedent (always true)"]
            )
        
        if rule.operator == LogicalOperator.AND:
            return self._prove_conjunction(rule.antecedent, depth, visited_rules, steps)
        elif rule.operator == LogicalOperator.OR:
            return self._prove_disjunction(rule.antecedent, depth, visited_rules, steps)
        else:
            # Default to AND for other operators
            return self._prove_conjunction(rule.antecedent, depth, visited_rules, steps)
    
    def _prove_conjunction(self, conditions: List[Condition], depth: int,
                          visited_rules: Set[str], steps: List[str]) -> InferenceResult:
        """Prove all conditions in a conjunction (AND)"""
        all_rule_chains = []
        all_steps = steps[:]
        min_confidence = 1.0
        
        for condition in conditions:
            result = self._infer_recursive(condition, depth, visited_rules, all_steps)
            if not result.success:
                return InferenceResult(
                    conclusion=condition,
                    rule_chain=[],
                    confidence=0.0,
                    steps=result.steps,
                    success=False,
                    error_message=f"Failed to prove: {condition}"
                )
            
            all_rule_chains.extend(result.rule_chain)
            all_steps = result.steps
            min_confidence = min(min_confidence, result.confidence)
        
        return InferenceResult(
            conclusion=conditions[0],  # Representative condition
            rule_chain=all_rule_chains,
            confidence=min_confidence,
            steps=all_steps
        )
    
    def _prove_disjunction(self, conditions: List[Condition], depth: int,
                          visited_rules: Set[str], steps: List[str]) -> InferenceResult:
        """Prove at least one condition in a disjunction (OR)"""
        best_result = None
        best_confidence = 0.0
        
        for condition in conditions:
            result = self._infer_recursive(condition, depth, visited_rules, steps)
            if result.success and result.confidence > best_confidence:
                best_result = result
                best_confidence = result.confidence
        
        if best_result:
            return best_result
        
        return InferenceResult(
            conclusion=conditions[0],
            rule_chain=[],
            confidence=0.0,
            steps=steps + ["No disjunct could be proven"],
            success=False,
            error_message="Disjunction failed"
        )
    
    def _validate_rule(self, rule: ConditionalRule) -> bool:
        """Validate a rule for consistency and correctness"""
        try:
            # Check for empty antecedent or consequent
            if not rule.consequent:
                self.logger.warning("Rule has empty consequent")
                return False
            
            # Check for circular dependencies (basic check)
            for ant in rule.antecedent:
                for con in rule.consequent:
                    if self._conditions_match(ant, con):
                        self.logger.warning(f"Circular rule detected: {rule}")
                        return False
            
            # Check confidence bounds
            if not (0.0 <= rule.confidence <= 1.0):
                self.logger.warning(f"Invalid confidence value: {rule.confidence}")
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"Rule validation error: {e}")
            return False
    
    def get_applicable_rules(self, condition: Condition) -> List[ConditionalRule]:
        """Get all rules that could potentially conclude the given condition"""
        applicable = []
        for rule in self.rules.values():
            if self._rule_concludes(rule, condition):
                applicable.append(rule)
        return applicable
    
    def explain_inference(self, query: Condition) -> str:
        """Provide a human-readable explanation of how a conclusion was reached"""
        result = self.infer(query)
        
        if not result.success:
            return f"Cannot prove '{query}': {result.error_message}"
        
        explanation = [f"Proving: {query}"]
        explanation.extend(result.steps)
        explanation.append(f"Conclusion: {query} (confidence: {result.confidence:.2f})")
        
        if result.rule_chain:
            explanation.append(f"Rules used: {', '.join(result.rule_chain)}")
        
        return "\n".join(explanation)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get engine statistics"""
        return {
            "total_rules": len(self.rules),
            "total_facts": len(self.facts),
            "cache_size": len(self.inference_cache),
            "rule_types": {rt.value: sum(1 for r in self.rules.values() if r.rule_type == rt) 
                          for rt in RuleType},
            "average_confidence": sum(r.confidence for r in self.rules.values()) / len(self.rules) 
                                 if self.rules else 0.0
        }
    
    def clear_cache(self) -> None:
        """Clear the inference cache"""
        self.inference_cache.clear()
        self.logger.debug("Inference cache cleared")
    
    def reset(self) -> None:
        """Reset the engine to initial state"""
        self.rules.clear()
        self.facts.clear()
        self.inference_cache.clear()
        self.logger.info("Conditional logic engine reset")