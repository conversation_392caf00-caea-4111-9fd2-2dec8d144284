#!/usr/bin/env python3
"""
Improved Human-Like Language Engine with modular architecture
"""

import time
from typing import Dict, Any, <PERSON>, Tuple, Optional
from knowledge_base import KnowledgeBase
from parser import SentenceParser, Question<PERSON>arser
from validator import SemanticValidator
from logger import logger, EngineError
from config import config

class KnowledgeEngineV2:
    """Improved knowledge engine with modular components"""
    
    def __init__(self, kb_file: Optional[str] = None):
        """Initialize the engine with all components"""
        try:
            logger.info("Initializing Knowledge Engine V2")
            
            # Initialize components
            self.knowledge_base = KnowledgeBase(kb_file)
            self.sentence_parser = SentenceParser()
            self.question_parser = QuestionParser()
            self.validator = SemanticValidator(self.knowledge_base)
            
            # Engine statistics
            self.stats = {
                'sentences_processed': 0,
                'questions_answered': 0,
                'facts_learned': 0,
                'validation_errors': 0,
                'parsing_errors': 0
            }
            
            logger.info("Knowledge Engine V2 initialized successfully", 
                       concepts=len(self.knowledge_base.knowledge))
            
        except Exception as e:
            logger.critical("Failed to initialize Knowledge Engine V2", exception=e)
            raise EngineError(f"Engine initialization failed: {e}")
    
    def process_sentence(self, text: str) -> Dict[str, Any]:
        """Process a sentence and return detailed results"""
        start_time = time.time()
        
        try:
            self.stats['sentences_processed'] += 1
            
            # Parse the sentence
            try:
                parsed = self.sentence_parser.parse(text)
                logger.debug(f"Sentence parsed", pattern=parsed['pattern'])
            except Exception as e:
                self.stats['parsing_errors'] += 1
                return {
                    'success': False,
                    'error_type': 'parsing',
                    'message': str(e),
                    'suggestions': [],
                    'processing_time': time.time() - start_time
                }
            
            # Validate structure
            structure_valid, structure_reason = self.sentence_parser.validate_parse_structure(parsed)
            if not structure_valid:
                self.stats['parsing_errors'] += 1
                return {
                    'success': False,
                    'error_type': 'structure',
                    'message': structure_reason,
                    'suggestions': [],
                    'processing_time': time.time() - start_time
                }
            
            # Validate semantics
            try:
                is_valid, reason, validation_details = self.validator.validate(parsed)
                
                if not is_valid:
                    self.stats['validation_errors'] += 1
                    suggestions = self.validator.get_validation_suggestions(
                        parsed.get('subject', ''),
                        parsed.get('verb', ''),
                        parsed.get('object')
                    )
                    
                    return {
                        'success': False,
                        'error_type': 'semantic',
                        'message': reason,
                        'suggestions': suggestions,
                        'validation_details': validation_details,
                        'parsed_sentence': parsed,
                        'processing_time': time.time() - start_time
                    }
                
                # Success case
                processing_time = time.time() - start_time
                logger.log_performance('sentence_processing', processing_time)
                
                return {
                    'success': True,
                    'message': reason,
                    'parsed_sentence': parsed,
                    'validation_details': validation_details,
                    'processing_time': processing_time
                }
                
            except Exception as e:
                self.stats['validation_errors'] += 1
                logger.error(f"Validation failed for sentence: '{text}'", exception=e)
                return {
                    'success': False,
                    'error_type': 'validation_error',
                    'message': f"Validation error: {str(e)}",
                    'suggestions': [],
                    'processing_time': time.time() - start_time
                }
                
        except Exception as e:
            logger.error(f"Unexpected error processing sentence: '{text}'", exception=e)
            return {
                'success': False,
                'error_type': 'unexpected',
                'message': f"Unexpected error: {str(e)}",
                'suggestions': [],
                'processing_time': time.time() - start_time
            }
    
    def answer_question(self, question: str) -> Dict[str, Any]:
        """Answer a question and return detailed results"""
        start_time = time.time()
        
        try:
            self.stats['questions_answered'] += 1
            
            # Parse the question
            question_data = self.question_parser.parse_question(question)
            
            if not question_data:
                return {
                    'success': False,
                    'message': "I don't understand that question format",
                    'supported_formats': [
                        "what can X do?",
                        "what is X?",
                        "who is X?",
                        "where is X?"
                    ],
                    'processing_time': time.time() - start_time
                }
            
            # Validate question semantics
            is_valid, validation_reason = self.validator.validate_question_semantics(question_data)
            
            # Generate answer based on question type
            answer = self._generate_answer(question_data)
            
            processing_time = time.time() - start_time
            logger.log_question_answered(question, answer)
            logger.log_performance('question_answering', processing_time)
            
            return {
                'success': True,
                'answer': answer,
                'question_type': question_data['type'],
                'subject': question_data['subject'],
                'validation_status': validation_reason,
                'processing_time': processing_time
            }
            
        except Exception as e:
            logger.error(f"Failed to answer question: '{question}'", exception=e)
            return {
                'success': False,
                'message': f"Error answering question: {str(e)}",
                'processing_time': time.time() - start_time
            }
    
    def _generate_answer(self, question_data: Dict[str, Any]) -> str:
        """Generate answer based on question type and subject"""
        question_type = question_data['type']
        subject = question_data['subject']
        
        if question_type == 'what_can_do':
            actions = self.knowledge_base.get_actions_for_subject(subject)
            if actions:
                return f"{subject} can: {', '.join(actions)}"
            elif self.knowledge_base.has_concept(subject):
                return f"{subject} cannot do any known actions"
            else:
                return f"I don't know about {subject}"
        
        elif question_type == 'what_is':
            concept = self.knowledge_base.get_concept(subject)
            if concept:
                concept_type = concept.get('type', 'unknown')
                meaning = concept.get('meaning', '')
                if meaning:
                    return f"{subject} is a {concept_type}: {meaning}"
                else:
                    return f"{subject} is a {concept_type}"
            else:
                return f"I don't know what {subject} is"
        
        elif question_type == 'who_is':
            concept = self.knowledge_base.get_concept(subject)
            if concept and concept.get('type') == 'animal':
                return f"{subject} is an animal"
            elif concept:
                return f"{subject} is a {concept.get('type', 'thing')}"
            else:
                return f"I don't know who {subject} is"
        
        elif question_type == 'where_is':
            # Basic implementation - could be extended
            return f"I don't know where {subject} is located"
        
        else:
            return "I don't understand that type of question"
    
    def learn_fact(self, subject: str, fact_type: str, fact_value: str) -> Dict[str, Any]:
        """Learn a new fact and return detailed results"""
        start_time = time.time()
        
        try:
            self.stats['facts_learned'] += 1
            
            # Validate input
            if not all([subject, fact_type, fact_value]):
                return {
                    'success': False,
                    'message': "Missing required parameters for learning",
                    'processing_time': time.time() - start_time
                }
            
            # Learn the fact
            result_message = self.knowledge_base.learn_fact(subject, fact_type, fact_value)
            
            processing_time = time.time() - start_time
            logger.log_performance('fact_learning', processing_time)
            
            return {
                'success': True,
                'message': result_message,
                'subject': subject,
                'fact_type': fact_type,
                'fact_value': fact_value,
                'processing_time': processing_time
            }
            
        except Exception as e:
            logger.error(f"Failed to learn fact", exception=e, 
                        subject=subject, fact_type=fact_type, fact_value=fact_value)
            return {
                'success': False,
                'message': f"Learning error: {str(e)}",
                'processing_time': time.time() - start_time
            }
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get comprehensive engine status and statistics"""
        try:
            kb_stats = self.knowledge_base.get_statistics()
            parser_stats = self.sentence_parser.get_parse_statistics()
            validator_stats = self.validator.get_validation_statistics()
            
            return {
                'engine_version': '2.0',
                'status': 'running',
                'statistics': self.stats,
                'knowledge_base': kb_stats,
                'parser': parser_stats,
                'validator': validator_stats,
                'configuration': {
                    'debug_mode': config.get('system.debug_mode', False),
                    'auto_save': config.get('knowledge_base.auto_save', True),
                    'max_vocabulary': config.get('system.max_vocabulary_size', 1000)
                }
            }
            
        except Exception as e:
            logger.error("Failed to get engine status", exception=e)
            return {
                'engine_version': '2.0',
                'status': 'error',
                'error': str(e)
            }
    
    def export_knowledge(self, filename: str) -> Dict[str, Any]:
        """Export knowledge base to file"""
        try:
            self.knowledge_base.export_learned_facts(filename)
            return {
                'success': True,
                'message': f"Knowledge exported to {filename}",
                'exported_facts': len(self.knowledge_base.learned_facts)
            }
        except Exception as e:
            logger.error(f"Failed to export knowledge", exception=e)
            return {
                'success': False,
                'message': f"Export failed: {str(e)}"
            }
    
    def reset_statistics(self) -> None:
        """Reset engine statistics"""
        self.stats = {
            'sentences_processed': 0,
            'questions_answered': 0,
            'facts_learned': 0,
            'validation_errors': 0,
            'parsing_errors': 0
        }
        logger.info("Engine statistics reset")


def main():
    """Test the improved engine"""
    try:
        print("🧠 Human-Like Language Engine V2")
        print("=================================\n")
        
        # Initialize engine
        engine = KnowledgeEngineV2()
        
        # Test sentences
        test_sentences = [
            "cat chase mouse",
            "dog bark",
            "table chase cat",  # Should fail
            "cat sleep",
            "mouse eat food",
            "unknown word here"  # Should fail
        ]
        
        print("📝 Testing Sentences:")
        for sentence in test_sentences:
            result = engine.process_sentence(sentence)
            status = "✅" if result['success'] else "❌"
            print(f"{status} '{sentence}' - {result['message']}")
            if not result['success'] and result.get('suggestions'):
                for suggestion in result['suggestions']:
                    print(f"   💡 {suggestion}")
        
        print("\n❓ Testing Questions:")
        questions = [
            "what can cat do?",
            "what is dog?",
            "what can unknown do?",
            "who is cat?"
        ]
        
        for question in questions:
            result = engine.answer_question(question)
            if result['success']:
                print(f"Q: {question}")
                print(f"A: {result['answer']}\n")
            else:
                print(f"❌ {question} - {result['message']}\n")
        
        print("📚 Testing Learning:")
        learn_result = engine.learn_fact("cat", "action", "jump")
        print(learn_result['message'])
        
        # Test the learned fact
        answer_result = engine.answer_question("what can cat do?")
        if answer_result['success']:
            print(f"Updated: {answer_result['answer']}")
        
        print("\n📊 Engine Status:")
        status = engine.get_engine_status()
        print(f"Processed: {status['statistics']['sentences_processed']} sentences")
        print(f"Answered: {status['statistics']['questions_answered']} questions")
        print(f"Learned: {status['statistics']['facts_learned']} facts")
        print(f"Knowledge base: {status['knowledge_base']['total_concepts']} concepts")
        
    except Exception as e:
        logger.critical("Main execution failed", exception=e)
        print(f"❌ Engine failed: {e}")

if __name__ == "__main__":
    main()