#!/usr/bin/env python3
"""
Knowledge Base management for the Human-Like Language Engine
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from config import config
from logger import logger, KnowledgeError

class KnowledgeBase:
    """Manages the knowledge base with persistence and versioning"""
    
    def __init__(self, kb_file: Optional[str] = None):
        self.kb_file = kb_file or config.get('knowledge_base.file_path', 'knowledge_base.json')
        self.knowledge: Dict[str, Any] = {}
        self.learned_facts: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        self.load_knowledge_base()
    
    def load_knowledge_base(self) -> None:
        """Load knowledge base from file"""
        try:
            if os.path.exists(self.kb_file):
                with open(self.kb_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.knowledge = data.get('concepts', {})
                self.learned_facts = data.get('learned_facts', {})
                self.metadata = data.get('metadata', {})
                
                logger.info(f"Loaded knowledge base with {len(self.knowledge)} concepts", 
                          file=self.kb_file)
            else:
                logger.warning(f"Knowledge base file not found: {self.kb_file}")
                self._create_empty_kb()
                
        except (json.JSONDecodeError, FileNotFoundError) as e:
            logger.error(f"Failed to load knowledge base", exception=e)
            raise KnowledgeError(f"Cannot load knowledge base: {e}")
    
    def save_knowledge_base(self) -> None:
        """Save knowledge base to file with backup"""
        try:
            # Create backup if enabled
            if config.get('knowledge_base.backup_enabled', True) and os.path.exists(self.kb_file):
                backup_file = f"{self.kb_file}.backup"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                os.rename(self.kb_file, backup_file)
                logger.debug(f"Created backup: {backup_file}")
            
            # Update metadata
            self.metadata.update({
                'last_modified': datetime.now().isoformat(),
                'total_concepts': len(self.knowledge),
                'learned_facts_count': len(self.learned_facts)
            })
            
            # Save to file
            data = {
                'concepts': self.knowledge,
                'learned_facts': self.learned_facts,
                'metadata': self.metadata
            }
            
            with open(self.kb_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved knowledge base", concepts=len(self.knowledge))
            
        except Exception as e:
            logger.error(f"Failed to save knowledge base", exception=e)
            raise KnowledgeError(f"Cannot save knowledge base: {e}")
    
    def _create_empty_kb(self) -> None:
        """Create an empty knowledge base structure"""
        self.knowledge = {}
        self.learned_facts = {}
        self.metadata = {
            'version': '1.0',
            'created': datetime.now().isoformat(),
            'total_concepts': 0
        }
        logger.info("Created empty knowledge base")
    
    def get_concept(self, concept: str) -> Optional[Dict[str, Any]]:
        """Get concept information"""
        return self.knowledge.get(concept.lower())
    
    def has_concept(self, concept: str) -> bool:
        """Check if concept exists"""
        return concept.lower() in self.knowledge
    
    def add_concept(self, concept: str, concept_data: Dict[str, Any]) -> None:
        """Add new concept to knowledge base"""
        concept_key = concept.lower()
        
        if concept_key in self.knowledge:
            logger.warning(f"Concept already exists: {concept}")
            return
        
        # Validate concept data structure
        required_fields = ['type']
        for field in required_fields:
            if field not in concept_data:
                raise KnowledgeError(f"Missing required field '{field}' for concept '{concept}'")
        
        self.knowledge[concept_key] = concept_data
        logger.info(f"Added new concept: {concept}", type=concept_data['type'])
        
        if config.get('knowledge_base.auto_save', True):
            self.save_knowledge_base()
    
    def update_concept(self, concept: str, updates: Dict[str, Any]) -> None:
        """Update existing concept"""
        concept_key = concept.lower()
        
        if concept_key not in self.knowledge:
            raise KnowledgeError(f"Concept not found: {concept}")
        
        self.knowledge[concept_key].update(updates)
        logger.info(f"Updated concept: {concept}", updates=list(updates.keys()))
        
        if config.get('knowledge_base.auto_save', True):
            self.save_knowledge_base()
    
    def learn_fact(self, subject: str, fact_type: str, fact_value: str) -> str:
        """Learn new fact about a subject"""
        subject_key = subject.lower()
        
        # Initialize subject if not exists
        if subject_key not in self.knowledge:
            self.knowledge[subject_key] = {
                'type': 'unknown',
                'actions': [],
                'properties': [],
                'meaning': f'learned concept: {subject}'
            }
        
        subject_data = self.knowledge[subject_key]
        result_message = ""
        
        if fact_type == 'action':
            if 'actions' not in subject_data:
                subject_data['actions'] = []
            if fact_value not in subject_data['actions']:
                subject_data['actions'].append(fact_value)
                result_message = f"Learned: {subject} can {fact_value}"
            else:
                result_message = f"Already know: {subject} can {fact_value}"
        
        elif fact_type == 'property':
            if 'properties' not in subject_data:
                subject_data['properties'] = []
            if fact_value not in subject_data['properties']:
                subject_data['properties'].append(fact_value)
                result_message = f"Learned: {subject} is {fact_value}"
            else:
                result_message = f"Already know: {subject} is {fact_value}"
        
        elif fact_type == 'type':
            old_type = subject_data.get('type', 'unknown')
            subject_data['type'] = fact_value
            result_message = f"Learned: {subject} is a {fact_value} (was {old_type})"
        
        else:
            raise KnowledgeError(f"Unknown fact type: {fact_type}")
        
        # Store in learned facts for tracking
        fact_key = f"{subject}_{fact_type}_{fact_value}"
        self.learned_facts[fact_key] = {
            'subject': subject,
            'fact_type': fact_type,
            'fact_value': fact_value,
            'learned_at': datetime.now().isoformat()
        }
        
        logger.log_learning_event(subject, fact_type, fact_value)
        
        if config.get('knowledge_base.auto_save', True):
            self.save_knowledge_base()
        
        return result_message
    
    def get_concepts_by_type(self, concept_type: str) -> List[str]:
        """Get all concepts of a specific type"""
        return [concept for concept, data in self.knowledge.items() 
                if data.get('type') == concept_type]
    
    def get_actions_for_subject(self, subject: str) -> List[str]:
        """Get all actions a subject can perform"""
        concept = self.get_concept(subject)
        if concept:
            return concept.get('actions', [])
        return []
    
    def get_properties_for_subject(self, subject: str) -> List[str]:
        """Get all properties of a subject"""
        concept = self.get_concept(subject)
        if concept:
            return concept.get('properties', [])
        return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get knowledge base statistics"""
        types_count = {}
        for concept_data in self.knowledge.values():
            concept_type = concept_data.get('type', 'unknown')
            types_count[concept_type] = types_count.get(concept_type, 0) + 1
        
        return {
            'total_concepts': len(self.knowledge),
            'learned_facts': len(self.learned_facts),
            'types_distribution': types_count,
            'last_modified': self.metadata.get('last_modified', 'unknown')
        }
    
    def export_learned_facts(self, filename: str) -> None:
        """Export learned facts to a separate file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.learned_facts, f, indent=2, ensure_ascii=False)
            logger.info(f"Exported {len(self.learned_facts)} learned facts", file=filename)
        except Exception as e:
            logger.error(f"Failed to export learned facts", exception=e)
            raise KnowledgeError(f"Cannot export learned facts: {e}")