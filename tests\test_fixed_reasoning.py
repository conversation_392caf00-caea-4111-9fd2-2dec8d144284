#!/usr/bin/env python3
"""
Test script to verify the fixed logical reasoning integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_web_interface_reasoning():
    """Test the web interface with fixed reasoning integration"""
    print("🧠 Testing Fixed Logical Reasoning Integration")
    print("=" * 50)
    
    try:
        from web_interface import WebInterface
        
        # Initialize web interface (this should now properly connect all components)
        print("Initializing Web Interface...")
        interface = WebInterface()
        
        # Check system status
        print("\n📊 System Status:")
        status = interface.get_system_status()
        for component, is_active in status.items():
            status_icon = "✅" if is_active else "❌"
            print(f"  {status_icon} {component}: {is_active}")
        
        # Test logical reasoning questions
        test_questions = [
            "Can cat breathe?",
            "Can cat fly?", 
            "Can mammal breathe?",
            "Can bird fly?",
            "Can fish swim?",
            "Can dog move?",
            "Can human think?"
        ]
        
        print(f"\n🔍 Testing Logical Reasoning:")
        print("-" * 30)
        
        for question in test_questions:
            print(f"\n❓ Question: {question}")
            
            # Process through all analysis types
            results = interface.process_text(question, 'all')
            
            # Check reasoning results
            reasoning_result = results.get('results', {}).get('reasoning', {})
            conditional_result = results.get('results', {}).get('conditional_logic', {})
            
            if reasoning_result.get('final_answer') is not None:
                answer = reasoning_result.get('final_answer')
                confidence = reasoning_result.get('confidence', 0)
                explanation = reasoning_result.get('explanation', '')
                print(f"  🧠 Reasoning Engine: {answer} (confidence: {confidence:.2f})")
                if explanation and explanation != "No supporting evidence found":
                    print(f"     Explanation: {explanation}")
            
            if conditional_result.get('success'):
                conclusion = conditional_result.get('conclusion', '')
                confidence = conditional_result.get('confidence', 0)
                steps = conditional_result.get('steps', [])
                print(f"  ⚡ Conditional Logic: {conclusion} (confidence: {confidence:.2f})")
                if steps:
                    print(f"     Steps: {steps[0] if steps else 'No steps'}")
            
            # Generate unified response
            chat_response = interface.generate_chat_response(question, results)
            print(f"  💬 Final Answer: {chat_response}")
            
            # Check for errors
            if results.get('errors'):
                print(f"  ⚠️  Errors: {results['errors']}")
        
        print(f"\n✅ Testing completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_base_integration():
    """Test knowledge base integration directly"""
    print("\n🗄️ Testing Knowledge Base Integration")
    print("-" * 40)
    
    try:
        from core.knowledge_base_v3 import HierarchyAwareKnowledgeBase
        from core.reasoning_chains import ReasoningChainEngine
        
        # Initialize components
        kb = HierarchyAwareKnowledgeBase('data/knowledge_base.json', 'config/hierarchy_schema.json')
        reasoning_engine = ReasoningChainEngine(knowledge_base=kb, hierarchy_manager=kb.hierarchy_manager)
        
        # Test specific concept
        print("📋 Testing 'cat' concept:")
        cat_concept = kb.get_concept('cat')
        if cat_concept:
            print(f"  Type: {cat_concept.get('type')}")
            print(f"  Direct actions: {cat_concept.get('actions', [])}")
            print(f"  Inherited actions: {cat_concept.get('inherited_actions', [])}")
            print(f"  All actions: {kb.get_all_actions_for_subject('cat')}")
        
        # Test reasoning directly
        print("\n🔗 Testing Direct Reasoning:")
        test_queries = ["can cat breathe?", "can cat fly?"]
        
        for query in test_queries:
            print(f"\n  Query: {query}")
            chain = reasoning_engine.reason(query)
            print(f"    Answer: {chain.final_answer}")
            print(f"    Confidence: {chain.overall_confidence:.2f}")
            print(f"    Steps: {len(chain.steps)}")
            print(f"    Explanation: {chain.explanation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Knowledge base test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Logical Reasoning Integration Tests")
    print("=" * 60)
    
    # Test 1: Web Interface Integration
    web_test_passed = test_web_interface_reasoning()
    
    # Test 2: Knowledge Base Integration  
    kb_test_passed = test_knowledge_base_integration()
    
    # Summary
    print(f"\n📊 Test Summary:")
    print(f"  Web Interface Test: {'✅ PASSED' if web_test_passed else '❌ FAILED'}")
    print(f"  Knowledge Base Test: {'✅ PASSED' if kb_test_passed else '❌ FAILED'}")
    
    if web_test_passed and kb_test_passed:
        print(f"\n🎉 All tests passed! Logical reasoning integration is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Please check the error messages above.")
