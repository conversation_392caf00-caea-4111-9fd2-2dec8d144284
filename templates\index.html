<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English LLM Testing Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .input-section {
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            min-height: 120px;
            transition: border-color 0.3s ease;
        }
        
        textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .analysis-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .option-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .option-card:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }
        
        .option-card.selected {
            border-color: #4facfe;
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .option-card input[type="radio"] {
            display: none;
        }
        
        .option-card .title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .option-card .description {
            font-size: 0.9em;
            color: #666;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-nav {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-nav:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3);
            color: white;
            text-decoration: none;
        }

        .navigation-buttons {
            margin-top: 15px;
            text-align: center;
        }
        
        .status-bar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.active {
            background: #28a745;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .result-card {
            background: #f8f9fa;
            border-left: 4px solid #4facfe;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .result-header {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .result-content {
            background: white;
            border-radius: 6px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }
        
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .history-section {
            margin-top: 30px;
            border-top: 2px solid #e9ecef;
            padding-top: 30px;
        }
        
        .history-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9em;
            color: #666;
        }
        
        .history-text {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .analysis-options {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 English LLM Testing Interface</h1>
            <p>Test and analyze text using advanced parsing, reasoning, and context understanding</p>
            <div class="navigation-buttons">
                <a href="/chat" class="btn-nav">💬 Chat Interface</a>
                <a href="/hierarchy" class="btn-nav">🌳 Hierarchy Viewer</a>
            </div>
        </div>
        
        <div class="main-content">
            <!-- System Status -->
            <div class="status-bar" id="statusBar">
                <div class="status-item">
                    <span class="status-indicator" id="enhancedParserStatus"></span>
                    <span>Enhanced Parser</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator" id="dependencyParserStatus"></span>
                    <span>Dependency Parser</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator" id="contextMemoryStatus"></span>
                    <span>Context Memory</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator" id="reasoningEngineStatus"></span>
                    <span>Reasoning Engine</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator" id="conditionalEngineStatus"></span>
                    <span>Conditional Logic</span>
                </div>
            </div>
            
            <!-- Input Section -->
            <div class="input-section">
                <div class="input-group">
                    <label for="textInput">Enter text to analyze:</label>
                    <textarea id="textInput" placeholder="Type your sentence or question here...\n\nExamples:\n• The cat that sits on the mat is sleeping.\n• If it rains tomorrow, I will stay home.\n• What is the capital of France?\n• John loves Mary, but she prefers Tom."></textarea>
                </div>
                
                <div class="input-group">
                    <label>Analysis Type:</label>
                    <div class="analysis-options">
                        <div class="option-card selected" data-value="all">
                            <input type="radio" name="analysisType" value="all" checked>
                            <div class="title">🔍 Complete Analysis</div>
                            <div class="description">Run all available analyzers</div>
                        </div>
                        <div class="option-card" data-value="enhanced_parsing">
                            <input type="radio" name="analysisType" value="enhanced_parsing">
                            <div class="title">📝 Enhanced Parsing</div>
                            <div class="description">Sentence structure & complexity</div>
                        </div>
                        <div class="option-card" data-value="dependency_parsing">
                            <input type="radio" name="analysisType" value="dependency_parsing">
                            <input type="radio" name="analysisType" value="dependency_parsing">
                            <div class="title">🔗 Dependency Parsing</div>
                            <div class="description">Grammatical relationships</div>
                        </div>
                        <div class="option-card" data-value="context_memory">
                            <input type="radio" name="analysisType" value="context_memory">
                            <div class="title">🧠 Context Memory</div>
                            <div class="description">Context tracking & history</div>
                        </div>
                        <div class="option-card" data-value="reasoning">
                            <input type="radio" name="analysisType" value="reasoning">
                            <div class="title">💭 Reasoning Chains</div>
                            <div class="description">Logical reasoning & inference</div>
                        </div>
                        <div class="option-card" data-value="conditional">
                            <input type="radio" name="analysisType" value="conditional">
                            <div class="title">⚡ Conditional Logic</div>
                            <div class="description">If-then statement analysis</div>
                        </div>
                    </div>
                </div>
                
                <button class="btn" onclick="analyzeText()">🚀 Analyze Text</button>
                <button class="btn btn-secondary" onclick="clearHistory()">🗑️ Clear History</button>
            </div>
            
            <!-- Loading -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Analyzing text...</p>
            </div>
            
            <!-- Results Section -->
            <div class="results-section" id="resultsSection" style="display: none;">
                <h2>📊 Analysis Results</h2>
                <div id="resultsContainer"></div>
            </div>
            
            <!-- History Section -->
            <div class="history-section">
                <h2>📚 Session History</h2>
                <div id="historyContainer">
                    <p style="text-align: center; color: #666; padding: 20px;">No analysis history yet. Start by analyzing some text!</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let systemStatus = {};
        
        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStatus();
            loadHistory();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Analysis type selection
            document.querySelectorAll('.option-card').forEach(card => {
                card.addEventListener('click', function() {
                    document.querySelectorAll('.option-card').forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    this.querySelector('input[type="radio"]').checked = true;
                });
            });
            
            // Enter key to analyze
            document.getElementById('textInput').addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    analyzeText();
                }
            });
        }
        
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/status');
                systemStatus = await response.json();
                updateStatusIndicators();
            } catch (error) {
                console.error('Failed to load system status:', error);
            }
        }
        
        function updateStatusIndicators() {
            const statusMap = {
                'enhancedParserStatus': 'enhanced_parser',
                'dependencyParserStatus': 'dependency_parser',
                'contextMemoryStatus': 'context_memory',
                'reasoningEngineStatus': 'reasoning_engine',
                'conditionalEngineStatus': 'conditional_engine'
            };
            
            Object.entries(statusMap).forEach(([elementId, statusKey]) => {
                const element = document.getElementById(elementId);
                if (element && systemStatus[statusKey]) {
                    element.classList.add('active');
                }
            });
        }
        
        async function analyzeText() {
            const textInput = document.getElementById('textInput');
            const text = textInput.value.trim();
            
            if (!text) {
                alert('Please enter some text to analyze.');
                return;
            }
            
            const analysisType = document.querySelector('input[name="analysisType"]:checked').value;
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        analysis_type: analysisType
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResults(result);
                    loadHistory(); // Refresh history
                } else {
                    displayError(result.error || 'Analysis failed');
                }
            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }
        
        function displayResults(result) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsSection = document.getElementById('resultsSection');
            
            resultsContainer.innerHTML = '';
            
            // Display errors if any
            if (result.errors && result.errors.length > 0) {
                result.errors.forEach(error => {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error';
                    errorDiv.textContent = error;
                    resultsContainer.appendChild(errorDiv);
                });
            }
            
            // Display results for each component
            Object.entries(result.results).forEach(([component, data]) => {
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                
                const header = document.createElement('div');
                header.className = 'result-header';
                header.textContent = formatComponentName(component);
                
                const content = document.createElement('div');
                content.className = 'result-content';
                
                const jsonViewer = document.createElement('div');
                jsonViewer.className = 'json-viewer';
                jsonViewer.textContent = JSON.stringify(data, null, 2);
                
                content.appendChild(jsonViewer);
                resultCard.appendChild(header);
                resultCard.appendChild(content);
                resultsContainer.appendChild(resultCard);
            });
            
            resultsSection.style.display = 'block';
        }
        
        function displayError(error) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsSection = document.getElementById('resultsSection');
            
            resultsContainer.innerHTML = `<div class="error">${error}</div>`;
            resultsSection.style.display = 'block';
        }
        
        function formatComponentName(component) {
            const names = {
                'enhanced_parsing': '📝 Enhanced Parsing',
                'dependency_parsing': '🔗 Dependency Parsing',
                'context_memory': '🧠 Context Memory',
                'reasoning': '💭 Reasoning Chains',
                'conditional_logic': '⚡ Conditional Logic'
            };
            return names[component] || component.replace('_', ' ').toUpperCase();
        }
        
        async function loadHistory() {
            try {
                const response = await fetch('/api/history?limit=5');
                const data = await response.json();
                displayHistory(data.history);
            } catch (error) {
                console.error('Failed to load history:', error);
            }
        }
        
        function displayHistory(history) {
            const historyContainer = document.getElementById('historyContainer');
            
            if (!history || history.length === 0) {
                historyContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No analysis history yet. Start by analyzing some text!</p>';
                return;
            }
            
            historyContainer.innerHTML = '';
            
            history.reverse().forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                
                const header = document.createElement('div');
                header.className = 'history-header';
                header.innerHTML = `
                    <span>Analysis Type: ${item.analysis_type}</span>
                    <span>${new Date(item.timestamp).toLocaleString()}</span>
                `;
                
                const text = document.createElement('div');
                text.className = 'history-text';
                text.textContent = item.input_text;
                
                const summary = document.createElement('div');
                summary.style.fontSize = '0.9em';
                summary.style.color = '#666';
                
                const componentCount = Object.keys(item.results).length;
                const errorCount = item.errors ? item.errors.length : 0;
                summary.textContent = `${componentCount} components analyzed, ${errorCount} errors`;
                
                historyItem.appendChild(header);
                historyItem.appendChild(text);
                historyItem.appendChild(summary);
                historyContainer.appendChild(historyItem);
            });
        }
        
        async function clearHistory() {
            if (!confirm('Are you sure you want to clear the session history?')) {
                return;
            }
            
            try {
                const response = await fetch('/api/clear_history', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    loadHistory();
                    document.getElementById('resultsSection').style.display = 'none';
                } else {
                    alert('Failed to clear history');
                }
            } catch (error) {
                alert('Error clearing history: ' + error.message);
            }
        }
    </script>
</body>
</html>