#!/usr/bin/env python3
"""
Debug script to check conditional logic engine facts
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_conditional_engine():
    """Debug the conditional logic engine facts"""
    print("🔍 Debugging Conditional Logic Engine")
    print("=" * 40)
    
    try:
        from web_interface import WebInterface
        
        # Initialize web interface
        interface = WebInterface()
        
        if interface.conditional_engine:
            print(f"📋 Facts in conditional engine: {len(interface.conditional_engine.facts)}")
            print("\n🔍 Sample facts:")
            for i, fact in enumerate(list(interface.conditional_engine.facts)[:10]):
                print(f"  {i+1}. {fact}")
            
            print(f"\n📋 Rules in conditional engine: {len(interface.conditional_engine.rules)}")
            print("\n🔍 Sample rules:")
            for i, (rule_id, rule) in enumerate(list(interface.conditional_engine.rules.items())[:5]):
                print(f"  {i+1}. {rule_id}: {rule}")
            
            # Test specific facts
            test_facts = [
                "cat can breathe",
                "cat is animal",
                "animal can breathe",
                "bird can fly",
                "animal is living_thing",
                "mammal is animal",
                "living_thing can breathe"
            ]
            
            print(f"\n🧪 Testing specific facts:")
            for fact in test_facts:
                exists = fact.lower() in interface.conditional_engine.facts
                print(f"  {'✅' if exists else '❌'} {fact}: {exists}")
            
            # Test inference
            print(f"\n🔗 Testing inference:")
            from core.conditional_logic import Condition
            
            test_conditions = [
                Condition("cat", "can", "breathe"),
                Condition("cat", "can", "fly"),
                Condition("bird", "can", "fly")
            ]
            
            for condition in test_conditions:
                result = interface.conditional_engine.infer(condition)
                print(f"  {condition}: success={result.success}, confidence={result.confidence:.2f}")
                if result.steps:
                    print(f"    Steps: {result.steps[0]}")
        
        else:
            print("❌ Conditional engine not initialized")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_conditional_engine()
