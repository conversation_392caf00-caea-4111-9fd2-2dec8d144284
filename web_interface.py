#!/usr/bin/env python3
"""
Web Interface for English LLM Testing
Provides a Flask-based web interface for testing all implemented features
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import json
import traceback
import os
from datetime import datetime
from typing import Dict, Any, List

# Import our core modules
try:
    from core.enhanced_parser import <PERSON>han<PERSON><PERSON>entenceParser
    from core.dependency_parser import DependencyParser
    from core.context_memory import ContextMemory
    from core.reasoning_chains import Reasoning<PERSON>hainEngine
    from core.conditional_logic import ConditionalLogicEngine
    from logger import logger
except ImportError as e:
    print(f"Import error: {e}")
    logger = None

app = Flask(__name__)
app.secret_key = 'english_llm_demo_key_2024'

# Initialize components
class WebInterface:
    def __init__(self):
        self.enhanced_parser = None
        self.dependency_parser = None
        self.context_memory = None
        self.reasoning_engine = None
        self.conditional_engine = None
        self.knowledge_base = None
        self.hierarchy_manager = None
        self.session_history = []

        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all available components"""
        # First initialize knowledge base and hierarchy manager
        try:
            from core.knowledge_base_v3 import HierarchyAwareKnowledgeBase
            from core.hierarchy_manager import HierarchyManager

            self.knowledge_base = HierarchyAwareKnowledgeBase('data/knowledge_base.json', 'config/hierarchy_schema.json')
            self.hierarchy_manager = self.knowledge_base.hierarchy_manager
            logger.info("Knowledge Base and Hierarchy Manager initialized") if logger else None
        except Exception as e:
            print(f"Failed to initialize Knowledge Base: {e}")
            # Create fallback instances
            try:
                from core.hierarchy_manager import HierarchyManager
                self.hierarchy_manager = HierarchyManager('config/hierarchy_schema.json')
            except:
                pass

        try:
            self.enhanced_parser = EnhancedSentenceParser()
            logger.info("Enhanced Parser initialized") if logger else None
        except Exception as e:
            print(f"Failed to initialize Enhanced Parser: {e}")

        try:
            self.dependency_parser = DependencyParser()
            logger.info("Dependency Parser initialized") if logger else None
        except Exception as e:
            print(f"Failed to initialize Dependency Parser: {e}")

        try:
            self.context_memory = ContextMemory()
            logger.info("Context Memory initialized") if logger else None
        except Exception as e:
            print(f"Failed to initialize Context Memory: {e}")

        try:
            # Initialize reasoning engine with knowledge base and hierarchy manager
            self.reasoning_engine = ReasoningChainEngine(
                knowledge_base=self.knowledge_base,
                hierarchy_manager=self.hierarchy_manager
            )
            logger.info("Reasoning Chain Engine initialized with knowledge base") if logger else None
        except Exception as e:
            print(f"Failed to initialize Reasoning Engine: {e}")

        try:
            self.conditional_engine = ConditionalLogicEngine()
            self._load_basic_knowledge()
            logger.info("Conditional Logic Engine initialized with basic knowledge") if logger else None
        except Exception as e:
            print(f"Failed to initialize Conditional Engine: {e}")
    
    def _load_basic_knowledge(self):
        """Load knowledge from knowledge base into the conditional logic engine"""
        if not self.conditional_engine or not self.knowledge_base:
            return

        from core.conditional_logic import Condition, ConditionalRule, RuleType, LogicalOperator

        # Load facts from knowledge base
        try:
            # Get all concepts from knowledge base
            for concept_name, concept_data in self.knowledge_base.knowledge.items():
                concept_type = concept_data.get('type', 'unknown')

                # Add type facts
                if concept_type != 'unknown':
                    self.conditional_engine.add_fact(f"{concept_name} is {concept_type}")

                # Add action facts (both direct and inherited)
                all_actions = concept_data.get('actions', []) + concept_data.get('inherited_actions', [])
                for action in all_actions:
                    self.conditional_engine.add_fact(f"{concept_name} can {action}")

                # Add property facts (both direct and inherited)
                all_properties = concept_data.get('properties', []) + concept_data.get('inherited_properties', [])
                for prop in all_properties:
                    self.conditional_engine.add_fact(f"{concept_name} is {prop}")

            # Add hierarchy-based rules and type relationships
            if self.hierarchy_manager:
                # First, add type hierarchy facts (parent-child relationships)
                for type_name, type_data in self.hierarchy_manager.hierarchy.items():
                    parent_type = type_data.get('parent')
                    if parent_type:
                        # Add the type hierarchy fact: "mammal is animal", "animal is living_thing", etc.
                        self.conditional_engine.add_fact(f"{type_name} is {parent_type}")

                # Add inheritance rules for each type in hierarchy
                for type_name in self.hierarchy_manager.hierarchy.keys():
                    # Add action inheritance rules
                    inherited_actions = self.hierarchy_manager.get_inherited_actions(type_name)
                    for action in inherited_actions:
                        rule = ConditionalRule(
                            rule_id=f"{type_name}_can_{action}",
                            rule_type=RuleType.IMPLICATION,
                            antecedent=[Condition("X", "is", type_name)],
                            consequent=[Condition("X", "can", action)],
                            confidence=0.9
                        )
                        self.conditional_engine.add_rule(rule)

                    # Add property inheritance rules
                    inherited_properties = self.hierarchy_manager.get_inherited_properties(type_name)
                    for prop in inherited_properties:
                        rule = ConditionalRule(
                            rule_id=f"{type_name}_is_{prop}",
                            rule_type=RuleType.IMPLICATION,
                            antecedent=[Condition("X", "is", type_name)],
                            consequent=[Condition("X", "is", prop)],
                            confidence=0.9
                        )
                        self.conditional_engine.add_rule(rule)

            logger.info("Loaded knowledge base facts and rules into conditional engine") if logger else None

        except Exception as e:
            print(f"Error loading knowledge base into conditional engine: {e}")
            # Fallback to basic hardcoded facts if knowledge base loading fails
            basic_facts = [
                "cat is mammal", "dog is mammal", "bird is animal", "fish is animal",
                "mammal is animal", "animal is living_thing",
                "living_thing can breathe", "animal can move", "mammal can move"
            ]
            for fact in basic_facts:
                self.conditional_engine.add_fact(fact)
    
    def process_text(self, text: str, analysis_type: str = 'all') -> Dict[str, Any]:
        """Process text through available components"""
        results = {
            'input_text': text,
            'timestamp': datetime.now().isoformat(),
            'analysis_type': analysis_type,
            'results': {},
            'errors': []
        }
        
        # Enhanced Parsing
        if analysis_type in ['all', 'enhanced_parsing'] and self.enhanced_parser:
            try:
                parse_result = self.enhanced_parser.parse(text)
                results['results']['enhanced_parsing'] = {
                    'sentence_type': parse_result.sentence_type.value if hasattr(parse_result.sentence_type, 'value') else str(parse_result.sentence_type),
                    'main_clause': {
                        'subject': parse_result.main_clause.subject if parse_result.main_clause else None,
                        'verb': parse_result.main_clause.verb if parse_result.main_clause else None,
                        'object': parse_result.main_clause.object if parse_result.main_clause else None
                    },
                    'subordinate_clauses': [{
                        'subject': clause.subject,
                        'verb': clause.verb,
                        'object': clause.object
                    } for clause in (parse_result.subordinate_clauses or [])],
                    'complexity_score': getattr(parse_result, 'complexity_score', 1.0),
                    'parse_confidence': getattr(parse_result, 'parse_confidence', 0.8)
                }
            except Exception as e:
                results['errors'].append(f"Enhanced Parsing error: {str(e)}")
        
        # Dependency Parsing
        if analysis_type in ['all', 'dependency_parsing'] and self.dependency_parser:
            try:
                dep_parse = self.dependency_parser.parse(text)
                structure = self.dependency_parser.analyze_sentence_structure(dep_parse)
                results['results']['dependency_parsing'] = {
                    'subjects': structure['subjects'],
                    'objects': structure['objects'],
                    'root_verb': structure['root_verb'],
                    'noun_phrases': structure['noun_phrases'],
                    'verb_phrases': structure['verb_phrases'],
                    'complexity_score': structure['complexity_score'],
                    'token_count': structure['token_count']
                }
            except Exception as e:
                results['errors'].append(f"Dependency Parsing error: {str(e)}")
        
        # Context Memory
        if analysis_type in ['all', 'context_memory'] and self.context_memory:
            try:
                # Add to context and get recent entries
                from core.context_memory import ContextType
                self.context_memory.add_entry(text, ContextType.QUESTION)
                recent_entries = self.context_memory.get_recent_entries(limit=5)
                results['results']['context_memory'] = {
                    'recent_entries': [{
                        'content': entry.content,
                        'context_type': entry.context_type.value if hasattr(entry.context_type, 'value') else str(entry.context_type),
                        'timestamp': datetime.fromtimestamp(entry.timestamp).isoformat()
                    } for entry in recent_entries],
                    'total_entries': len(self.context_memory.history)
                }
            except Exception as e:
                results['errors'].append(f"Context Memory error: {str(e)}")
        
        # Reasoning Chains
        if analysis_type in ['all', 'reasoning'] and self.reasoning_engine:
            try:
                reasoning_result = self.reasoning_engine.reason(text)
                results['results']['reasoning'] = {
                    'query': reasoning_result.query,
                    'final_answer': reasoning_result.final_answer,
                    'confidence': reasoning_result.overall_confidence,
                    'steps_count': len(reasoning_result.steps),
                    'explanation': reasoning_result.explanation,
                    'total_time': reasoning_result.total_time
                }
            except Exception as e:
                results['errors'].append(f"Reasoning error: {str(e)}")
        
        # Conditional Logic
        if analysis_type in ['all', 'conditional'] and self.conditional_engine:
            try:
                # Create a simple condition from the text for inference
                from core.conditional_logic import Condition
                # Parse simple "can X Y" questions
                if text.lower().startswith('can '):
                    parts = text[4:].strip().rstrip('?').split()
                    if len(parts) >= 2:
                        subject = parts[0]
                        action = ' '.join(parts[1:])
                        
                        # First check for direct positive fact
                        positive_fact = f"{subject} can {action}"
                        negative_fact = f"{subject} cannot {action}"
                        
                        if positive_fact.lower() in self.conditional_engine.facts:
                            results['results']['conditional_logic'] = {
                                'query': text,
                                'conclusion': 'True',
                                'success': True,
                                'confidence': 1.0,
                                'rule_chain': [],
                                'steps': [f"Direct fact: {positive_fact}"]
                            }
                        elif negative_fact.lower() in self.conditional_engine.facts:
                            results['results']['conditional_logic'] = {
                                'query': text,
                                'conclusion': 'False',
                                'success': True,
                                'confidence': 1.0,
                                'rule_chain': [],
                                'steps': [f"Direct fact: {negative_fact}"]
                            }
                        else:
                            # Try inference if no direct fact found
                            condition = Condition(subject, 'can', action)
                            inference_result = self.conditional_engine.infer(condition)
                            results['results']['conditional_logic'] = {
                                'query': text,
                                'conclusion': str(inference_result.conclusion),
                                'success': inference_result.success,
                                'confidence': inference_result.confidence,
                                'rule_chain': inference_result.rule_chain,
                                'steps': inference_result.steps[:3]  # Limit steps for display
                            }
                    else:
                        results['results']['conditional_logic'] = {
                            'query': text,
                            'message': 'Could not parse question format'
                        }
                else:
                    results['results']['conditional_logic'] = {
                        'query': text,
                        'message': 'Only "can X Y?" questions supported currently'
                    }
            except Exception as e:
                results['errors'].append(f"Conditional Logic error: {str(e)}")
        
        # Add to session history
        self.session_history.append(results)
        
        return results
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get status of all components"""
        status = {
            'enhanced_parser': self.enhanced_parser is not None,
            'dependency_parser': self.dependency_parser is not None,
            'context_memory': self.context_memory is not None,
            'reasoning_engine': self.reasoning_engine is not None,
            'conditional_engine': self.conditional_engine is not None,
            'knowledge_base': self.knowledge_base is not None,
            'hierarchy_manager': self.hierarchy_manager is not None,
            'session_entries': len(self.session_history)
        }
        
        # Get model info if available
        if self.dependency_parser:
            try:
                status['dependency_model'] = self.dependency_parser.get_model_info()
            except:
                pass
        
        return status
    
    def generate_chat_response(self, text: str, analysis_results: Dict[str, Any]) -> str:
        """Generate a unified chatbot response from analysis results"""
        try:
            # Check if it's a question that can be answered directly
            if text.lower().startswith('can '):
                return self._handle_ability_question(text, analysis_results)
            elif '?' in text:
                return self._handle_general_question(text, analysis_results)
            else:
                return self._handle_statement_analysis(text, analysis_results)
        except Exception as e:
            return f"I analyzed your text but encountered an issue generating a response: {str(e)}"
    
    def _handle_ability_question(self, text: str, results: Dict[str, Any]) -> str:
        """Handle 'can X do Y?' type questions"""
        conditional_result = results.get('results', {}).get('conditional_logic', {})
        reasoning_result = results.get('results', {}).get('reasoning', {})

        # Try conditional logic first
        if conditional_result.get('success'):
            conclusion = conditional_result.get('conclusion', '').lower()
            confidence = conditional_result.get('confidence', 0)

            # Determine answer based on conclusion
            if conclusion == 'true' or 'can' in conclusion:
                answer = "Yes"
            elif conclusion == 'false' or 'cannot' in conclusion:
                answer = "No"
            else:
                answer = "I'm not sure"

            # Extract the specific ability from the question
            parts = text[4:].strip().rstrip('?').split()
            if len(parts) >= 2:
                subject = parts[0]
                action = ' '.join(parts[1:])
                response = f"{answer}, {subject} {'can' if answer == 'Yes' else 'cannot'} {action}."
            else:
                response = f"{answer}."

            # Add confidence level
            if confidence >= 0.8:
                response += " I'm quite confident about this."
            elif confidence >= 0.5:
                response += " I'm moderately confident about this."
            else:
                response += " I'm not very confident about this answer."

            # Add reasoning if available
            steps = conditional_result.get('steps', [])
            if steps and len(steps) > 0:
                step = steps[0]
                if 'Direct fact:' in step:
                    response += f" This is based on my knowledge that {step.replace('Direct fact: ', '')}."
                else:
                    response += f" My reasoning: {step}"

            return response

        # Fall back to reasoning engine if conditional logic fails
        elif reasoning_result.get('final_answer') is not None:
            answer_bool = reasoning_result.get('final_answer')
            confidence = reasoning_result.get('confidence', 0)
            explanation = reasoning_result.get('explanation', '')

            # Extract the specific ability from the question
            parts = text[4:].strip().rstrip('?').split()
            if len(parts) >= 2:
                subject = parts[0]
                action = ' '.join(parts[1:])
                answer_text = "Yes" if answer_bool else "No"
                response = f"{answer_text}, {subject} {'can' if answer_bool else 'cannot'} {action}."
            else:
                response = f"{'Yes' if answer_bool else 'No'}."

            # Add confidence level
            if confidence >= 0.8:
                response += " I'm quite confident about this."
            elif confidence >= 0.5:
                response += " I'm moderately confident about this."
            else:
                response += " I'm not very confident about this answer."

            # Add explanation if available
            if explanation and explanation != "No supporting evidence found":
                response += f" {explanation}"

            return response

        else:
            return f"I'm not sure about '{text}'. I don't have enough information to answer this question confidently."
    
    def _handle_general_question(self, text: str, results: Dict[str, Any]) -> str:
        """Handle general questions"""
        reasoning_result = results.get('results', {}).get('reasoning', {})
        
        if reasoning_result.get('final_answer'):
            answer = reasoning_result.get('final_answer')
            confidence = reasoning_result.get('confidence', 0)
            explanation = reasoning_result.get('explanation', '')
            
            response = f"Based on my analysis: {answer}"
            
            if explanation and explanation != "No supporting evidence found":
                response += f" {explanation}"
            
            if confidence >= 0.7:
                response += " I'm confident in this answer."
            elif confidence >= 0.4:
                response += " I have moderate confidence in this answer."
            else:
                response += " I'm not very confident about this."
            
            return response
        else:
            return self._provide_analysis_summary(text, results)
    
    def _handle_statement_analysis(self, text: str, results: Dict[str, Any]) -> str:
        """Handle statement analysis"""
        return self._provide_analysis_summary(text, results)
    
    def _provide_analysis_summary(self, text: str, results: Dict[str, Any]) -> str:
        """Provide a summary of the analysis"""
        response_parts = []
        
        # Enhanced parsing insights
        enhanced = results.get('results', {}).get('enhanced_parsing', {})
        if enhanced:
            sentence_type = enhanced.get('sentence_type', 'unknown')
            complexity = enhanced.get('complexity_score', 1)
            
            if sentence_type != 'unknown':
                response_parts.append(f"This is a {sentence_type} sentence")
            
            if complexity > 2:
                response_parts.append("with high complexity")
            elif complexity > 1.5:
                response_parts.append("with moderate complexity")
        
        # Dependency parsing insights
        dependency = results.get('results', {}).get('dependency_parsing', {})
        if dependency:
            subjects = dependency.get('subjects', [])
            root_verb = dependency.get('root_verb')
            
            if subjects and root_verb:
                if len(subjects) == 1:
                    response_parts.append(f"The main subject is '{subjects[0]}' and the key action is '{root_verb}'")
                elif len(subjects) > 1:
                    response_parts.append(f"The main subjects are {', '.join(subjects)} and the key action is '{root_verb}'")
        
        # Context insights
        context = results.get('results', {}).get('context_memory', {})
        if context:
            total_entries = context.get('total_entries', 0)
            if total_entries > 1:
                response_parts.append(f"This is part of our ongoing conversation (message #{total_entries})")
        
        if response_parts:
            return f"I analyzed your text: {'. '.join(response_parts)}."
        else:
            return "I've analyzed your text, but I don't have a specific answer or insight to share about it."

# Global interface instance
web_interface = WebInterface()

@app.route('/')
def index():
    """Main interface page"""
    return render_template('index.html')

@app.route('/chat')
def chat_interface():
    """Serve the chatbot interface"""
    return render_template('chat.html')

@app.route('/hierarchy')
def hierarchy():
    """Serve the hierarchy visualization interface"""
    return render_template('hierarchy.html')

@app.route('/api/hierarchy')
def api_hierarchy():
    """API endpoint to get hierarchy data"""
    try:
        # Load hierarchy data from the schema file
        import json
        from pathlib import Path
        
        schema_path = Path('config/hierarchy_schema.json')
        if schema_path.exists():
            with open(schema_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return jsonify({
                    'success': True,
                    'hierarchy': data.get('hierarchy', {})
                })
        else:
            return jsonify({
                'success': False,
                'error': 'Hierarchy schema file not found'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analyze', methods=['POST'])
def analyze_text():
    """Analyze text through selected components"""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        analysis_type = data.get('analysis_type', 'all')
        
        if not text:
            return jsonify({'error': 'No text provided'}), 400
        
        results = web_interface.process_text(text, analysis_type)
        return jsonify(results)
    
    except Exception as e:
        logger.error(f"Analysis error: {str(e)}") if logger else None
        return jsonify({
            'error': f'Analysis failed: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/status')
def get_status():
    """Get system status"""
    try:
        status = web_interface.get_system_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/history')
def get_history():
    """Get session history"""
    try:
        limit = request.args.get('limit', 10, type=int)
        history = web_interface.session_history[-limit:] if limit > 0 else web_interface.session_history
        return jsonify({
            'history': history,
            'total_count': len(web_interface.session_history)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear_history', methods=['POST'])
def clear_history():
    """Clear session history"""
    try:
        web_interface.session_history.clear()
        if web_interface.context_memory:
            web_interface.context_memory.clear_history()
        return jsonify({'message': 'History cleared successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat_response():
    """Generate a unified chatbot response"""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        
        if not text:
            return jsonify({'error': 'No text provided'}), 400
        
        # Get analysis from all components
        results = web_interface.process_text(text, 'all')
        
        # Generate unified response
        response = web_interface.generate_chat_response(text, results)
        
        return jsonify({
            'response': response,
            'timestamp': datetime.now().isoformat(),
            'query': text
        })
    
    except Exception as e:
        logger.error(f"Chat error: {str(e)}") if logger else None
        return jsonify({
            'error': f'Chat failed: {str(e)}',
            'response': 'Sorry, I encountered an error while processing your message.'
        }), 500

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

if __name__ == '__main__':
    # Create templates and static directories if they don't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("🚀 Starting English LLM Web Interface...")
    print("📊 System Status:")
    status = web_interface.get_system_status()
    for component, available in status.items():
        if isinstance(available, bool):
            emoji = "✅" if available else "❌"
            print(f"   {emoji} {component.replace('_', ' ').title()}")
    
    print("\n🌐 Access the interface at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)