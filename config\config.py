#!/usr/bin/env python3
"""
Configuration management for the Human-Like Language Engine
"""

import json
import os
from typing import Dict, Any, List

class Config:
    """Configuration manager for the language engine"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            "system": {
                "max_vocabulary_size": 1000,
                "max_sentence_length": 10,
                "learning_rate": 0.1,
                "debug_mode": False
            },
            "grammar": {
                "sentence_patterns": {
                    "svo": ["subject", "verb", "object"],
                    "sv": ["subject", "verb"]
                },
                "supported_patterns": ["svo", "sv"]
            },
            "knowledge_base": {
                "file_path": "knowledge_base.json",
                "auto_save": True,
                "backup_enabled": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "engine.log",
                "console_output": True
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults for missing keys
                return self._merge_configs(self.default_config, config)
            except (json.JSONDecodeError, FileNotFoundError):
                print(f"Warning: Could not load {self.config_file}, using defaults")
        
        # Create default config file
        self.save_config(self.default_config)
        return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None) -> None:
        """Save configuration to file"""
        config_to_save = config or self.config
        with open(self.config_file, 'w') as f:
            json.dump(config_to_save, f, indent=2)
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """Recursively merge user config with defaults"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key_path: str, default=None):
        """Get config value using dot notation (e.g., 'system.debug_mode')"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any) -> None:
        """Set config value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        
        if self.get('knowledge_base.auto_save', True):
            self.save_config()

# Global config instance
config = Config()