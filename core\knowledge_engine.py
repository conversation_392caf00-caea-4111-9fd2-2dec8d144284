#!/usr/bin/env python3
"""
Minimal Human-Like Language Learning Engine
Core Knowledge Store and Basic Reasoning
"""

class KnowledgeEngine:
    def __init__(self):
        # Ultra-minimal knowledge base - start with just 20 concepts
        self.knowledge = {
            # Animals
            'cat': {
                'type': 'animal',
                'actions': ['chase', 'sleep', 'eat', 'meow'],
                'properties': ['furry', 'small']
            },
            'dog': {
                'type': 'animal', 
                'actions': ['bark', 'run', 'eat', 'sleep'],
                'properties': ['furry', 'loyal']
            },
            'mouse': {
                'type': 'animal',
                'actions': ['run', 'hide', 'eat'],
                'properties': ['small', 'quick']
            },
            
            # Actions
            'chase': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': ['animal'],
                'meaning': 'to follow quickly to catch'
            },
            'bark': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': [],
                'meaning': 'to make a loud sound'
            },
            'meow': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': [],
                'meaning': 'to make a cat sound'
            },
            'run': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': [],
                'meaning': 'to move quickly'
            },
            'hide': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': [],
                'meaning': 'to conceal oneself'
            },
            'eat': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': ['food', 'animal'],
                'meaning': 'to consume food'
            },
            'sleep': {
                'type': 'action',
                'requires_subject': ['animal'],
                'requires_object': [],
                'meaning': 'to rest'
            },
            
            # Objects
            'food': {
                'type': 'food',
                'properties': ['edible']
            },
            'table': {
                'type': 'object',
                'properties': ['solid', 'furniture']
            }
        }
        
        # Grammar rules - very basic
        self.grammar_rules = {
            'sentence_patterns': {
                'svo': ['subject', 'verb', 'object'],  # cat chase mouse
                'sv': ['subject', 'verb']              # cat sleep
            }
        }
    
    def parse_sentence(self, text):
        """Basic sentence parsing - split by spaces"""
        words = text.lower().strip().split()
        
        if len(words) == 3:
            return {
                'pattern': 'svo',
                'subject': words[0],
                'verb': words[1], 
                'object': words[2]
            }
        elif len(words) == 2:
            return {
                'pattern': 'sv',
                'subject': words[0],
                'verb': words[1],
                'object': None
            }
        else:
            return None
    
    def validate_sentence(self, parsed):
        """Check if sentence makes logical sense"""
        if not parsed:
            return False, "Cannot parse sentence"
            
        subject = parsed['subject']
        verb = parsed['verb']
        obj = parsed.get('object')
        
        # Check if words exist in knowledge base
        if subject not in self.knowledge:
            return False, f"Unknown subject: {subject}"
        if verb not in self.knowledge:
            return False, f"Unknown verb: {verb}"
        if obj and obj not in self.knowledge:
            return False, f"Unknown object: {obj}"
        
        # Check if subject can perform this action
        subject_info = self.knowledge[subject]
        if verb not in subject_info.get('actions', []):
            return False, f"{subject} cannot {verb}"
        
        # Check verb requirements
        verb_info = self.knowledge[verb]
        
        # Check subject type compatibility
        required_subject_types = verb_info.get('requires_subject', [])
        if required_subject_types and subject_info['type'] not in required_subject_types:
            return False, f"{verb} requires subject of type {required_subject_types}, got {subject_info['type']}"
        
        # Check object requirements if object exists
        if obj:
            required_object_types = verb_info.get('requires_object', [])
            obj_info = self.knowledge[obj]
            if required_object_types and obj_info['type'] not in required_object_types:
                return False, f"{verb} requires object of type {required_object_types}, got {obj_info['type']}"
        
        return True, "Valid sentence"
    
    def answer_question(self, question):
        """Answer simple questions about concepts"""
        question = question.lower().strip()
        
        # "what can X do?"
        if question.startswith("what can ") and question.endswith(" do?"):
            subject = question[9:-4]  # extract subject
            if subject in self.knowledge:
                actions = self.knowledge[subject].get('actions', [])
                if actions:
                    return f"{subject} can: {', '.join(actions)}"
                else:
                    return f"{subject} cannot do any known actions"
            else:
                return f"I don't know about {subject}"
        
        # "what is X?"
        elif question.startswith("what is ") and question.endswith("?"):
            subject = question[8:-1]  # extract subject
            if subject in self.knowledge:
                info = self.knowledge[subject]
                return f"{subject} is a {info['type']}"
            else:
                return f"I don't know what {subject} is"
        
        return "I don't understand that question"
    
    def learn_new_fact(self, subject, fact_type, fact_value):
        """Add new knowledge - very basic learning"""
        if subject not in self.knowledge:
            self.knowledge[subject] = {'type': 'unknown', 'actions': [], 'properties': []}
        
        if fact_type == 'action' and fact_value not in self.knowledge[subject]['actions']:
            self.knowledge[subject]['actions'].append(fact_value)
            return f"Learned: {subject} can {fact_value}"
        elif fact_type == 'property' and fact_value not in self.knowledge[subject]['properties']:
            self.knowledge[subject]['properties'].append(fact_value)
            return f"Learned: {subject} is {fact_value}"
        elif fact_type == 'type':
            self.knowledge[subject]['type'] = fact_value
            return f"Learned: {subject} is a {fact_value}"
        
        return "Already know that fact"


def main():
    """Test the minimal engine"""
    engine = KnowledgeEngine()
    
    print("🧠 Minimal Human-Like Language Engine")
    print("=====================================\n")
    
    # Test sentences
    test_sentences = [
        "cat chase mouse",
        "dog bark", 
        "table chase cat",  # Should fail
        "cat sleep",
        "mouse eat food"
    ]
    
    print("📝 Testing Sentences:")
    for sentence in test_sentences:
        parsed = engine.parse_sentence(sentence)
        valid, reason = engine.validate_sentence(parsed)
        status = "✅" if valid else "❌"
        print(f"{status} '{sentence}' - {reason}")
    
    print("\n❓ Testing Questions:")
    questions = [
        "what can cat do?",
        "what is dog?",
        "what can table do?"
    ]
    
    for question in questions:
        answer = engine.answer_question(question)
        print(f"Q: {question}")
        print(f"A: {answer}\n")
    
    print("📚 Testing Learning:")
    print(engine.learn_new_fact("cat", "action", "jump"))
    print(engine.answer_question("what can cat do?"))

if __name__ == "__main__":
    main()