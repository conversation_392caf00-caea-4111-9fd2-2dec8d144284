import time

print("Attempting to import libraries...")
start_time = time.time()

try:
    import numpy
    print(f"Successfully imported numpy in {time.time() - start_time:.2f} seconds.")
    
    import spacy
    print(f"Successfully imported spacy in {time.time() - start_time:.2f} seconds.")
    
    print("\nLoading SpaCy model 'en_core_web_sm'...")
    nlp = spacy.load("en_core_web_sm")
    print(f"Successfully loaded SpaCy model in {time.time() - start_time:.2f} seconds.")
    
    print("\n✅ All major libraries and models loaded successfully.")
    
except Exception as e:
    print(f"\n❌ An error occurred: {e}")
