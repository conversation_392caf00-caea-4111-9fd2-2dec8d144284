#!/usr/bin/env python3
"""
Final test to demonstrate the fixed logical reasoning capabilities
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_logical_reasoning_examples():
    """Test the logical reasoning with real-world examples"""
    print("🧠 Final Logical Reasoning Test")
    print("=" * 40)
    
    try:
        from web_interface import WebInterface
        
        # Initialize web interface
        interface = WebInterface()
        
        # Test hierarchical logical reasoning
        test_cases = [
            {
                'question': 'Can cat breathe?',
                'expected_logic': 'cat → animal → living_thing → can breathe',
                'expected_answer': 'Yes'
            },
            {
                'question': 'Can dog move?', 
                'expected_logic': 'dog → mammal → animal → can move',
                'expected_answer': 'Yes'
            },
            {
                'question': 'Can fish swim?',
                'expected_logic': 'fish → animal → can swim (direct)',
                'expected_answer': 'Yes'
            },
            {
                'question': 'Can bird fly?',
                'expected_logic': 'bird → can fly (direct)',
                'expected_answer': 'Yes'
            },
            {
                'question': 'Can cat fly?',
                'expected_logic': 'cat → mammal → animal (no fly capability)',
                'expected_answer': 'No'
            },
            {
                'question': 'Can human think?',
                'expected_logic': 'human → can think (direct)',
                'expected_answer': 'Yes'
            }
        ]
        
        print("🔍 Testing Hierarchical Logical Reasoning:")
        print("-" * 50)
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            question = test_case['question']
            expected_answer = test_case['expected_answer']
            expected_logic = test_case['expected_logic']
            
            print(f"\n{i}. Question: {question}")
            print(f"   Expected Logic: {expected_logic}")
            print(f"   Expected Answer: {expected_answer}")
            
            # Process the question
            results = interface.process_text(question, 'all')
            chat_response = interface.generate_chat_response(question, results)
            
            # Check if the answer is correct
            answer_correct = expected_answer.lower() in chat_response.lower()
            
            print(f"   💬 System Answer: {chat_response}")
            print(f"   ✅ Result: {'PASS' if answer_correct else 'FAIL'}")
            
            if answer_correct:
                passed_tests += 1
            
            # Show reasoning details
            reasoning_result = results.get('results', {}).get('reasoning', {})
            if reasoning_result.get('final_answer') is not None:
                confidence = reasoning_result.get('confidence', 0)
                explanation = reasoning_result.get('explanation', '')
                print(f"   🧠 Reasoning: confidence={confidence:.2f}, {explanation}")
        
        print(f"\n📊 Final Results:")
        print(f"   Passed: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        if passed_tests == total_tests:
            print(f"   🎉 ALL TESTS PASSED! Logical reasoning is working correctly.")
            return True
        else:
            print(f"   ⚠️  Some tests failed. Please review the results above.")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_reasoning_process():
    """Demonstrate the step-by-step reasoning process"""
    print(f"\n🔗 Demonstrating Step-by-Step Reasoning Process")
    print("-" * 50)
    
    try:
        from core.knowledge_base_v3 import HierarchyAwareKnowledgeBase
        from core.reasoning_chains import ReasoningChainEngine
        
        # Initialize components
        kb = HierarchyAwareKnowledgeBase('data/knowledge_base.json', 'config/hierarchy_schema.json')
        reasoning_engine = ReasoningChainEngine(knowledge_base=kb, hierarchy_manager=kb.hierarchy_manager)
        
        # Demonstrate reasoning for "Can cat breathe?"
        print("Example: 'Can cat breathe?'")
        print("Expected reasoning chain:")
        print("  1. cat → is type → animal")
        print("  2. animal → is type → living_thing") 
        print("  3. living_thing → can → breathe")
        print("  4. Therefore: cat can breathe ✅")
        
        chain = reasoning_engine.reason("can cat breathe?")
        print(f"\nActual reasoning result:")
        print(f"  Final Answer: {chain.final_answer}")
        print(f"  Confidence: {chain.overall_confidence:.2f}")
        print(f"  Steps: {len(chain.steps)}")
        print(f"  Explanation: {chain.explanation}")
        
        if chain.steps:
            print(f"  Reasoning Steps:")
            for i, step in enumerate(chain.steps, 1):
                print(f"    {i}. {step.reasoning_type.value}: {step.explanation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Demonstration failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Final Logical Reasoning Integration Test")
    print("=" * 60)
    
    # Test 1: Logical reasoning examples
    test_passed = test_logical_reasoning_examples()
    
    # Test 2: Demonstrate reasoning process
    demo_passed = demonstrate_reasoning_process()
    
    # Final summary
    print(f"\n🏁 FINAL SUMMARY:")
    print(f"   Logical Reasoning Test: {'✅ PASSED' if test_passed else '❌ FAILED'}")
    print(f"   Reasoning Demonstration: {'✅ PASSED' if demo_passed else '❌ FAILED'}")
    
    if test_passed and demo_passed:
        print(f"\n🎉 SUCCESS! The logical reasoning integration is working correctly!")
        print(f"   The system can now properly reason through hierarchical relationships")
        print(f"   like 'cats can breathe because they are animals and animals are living things'.")
    else:
        print(f"\n⚠️  Some issues remain. Please check the test results above.")
