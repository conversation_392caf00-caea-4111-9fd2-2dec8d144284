#!/usr/bin/env python3
"""
Test Suite for Dependency Parser
Tests SpaCy-based dependency parsing and fallback rules
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.dependency_parser import (
        DependencyParser, DependencyParse, DependencyToken, 
        DependencyRelation, SPACY_AVAILABLE
    )
except ImportError as e:
    print(f"Import error: {e}")

try:
    from logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    # Fallback implementations for testing
    from enum import Enum
    from dataclasses import dataclass
    from typing import List, Dict, Any, Optional
    
    SPACY_AVAILABLE = False
    
    class DependencyRelation(Enum):
        SUBJECT = "nsubj"
        OBJECT = "dobj"
        ROOT = "ROOT"
    
    @dataclass
    class DependencyToken:
        text: str
        lemma: str
        pos: str
        tag: str
        dep: str
        head_idx: int
        children_idx: List[int]
        is_root: bool = False
    
    @dataclass
    class DependencyParse:
        tokens: List[DependencyToken]
        root_idx: int
        sentence: str
        entities: List[Dict[str, Any]]
        noun_phrases: List[str]
        verb_phrases: List[str]
        dependencies: Dict[str, List[int]]
    
    class DependencyParser:
        def __init__(self, model_name: str = "en_core_web_sm"):
            self.use_spacy = False
            self.model_name = model_name
        
        def parse(self, text: str) -> DependencyParse:
            words = text.split()
            tokens = []
            for i, word in enumerate(words):
                token = DependencyToken(
                    text=word, lemma=word, pos="NOUN", tag="NN",
                    dep="ROOT" if i == 1 else "nsubj" if i == 0 else "dobj",
                    head_idx=1, children_idx=[], is_root=(i == 1)
                )
                tokens.append(token)
            
            return DependencyParse(
                tokens=tokens, root_idx=1, sentence=text,
                entities=[], noun_phrases=[words[0]], verb_phrases=[words[1]],
                dependencies={"nsubj": [0], "ROOT": [1], "dobj": [2] if len(words) > 2 else []}
            )
        
        def get_subjects(self, parse: DependencyParse) -> List[str]:
            return ["cat"]
        
        def get_objects(self, parse: DependencyParse) -> List[str]:
            return ["fish"]
        
        def get_root_verb(self, parse: DependencyParse) -> Optional[str]:
            return "eats"
        
        def analyze_sentence_structure(self, parse: DependencyParse) -> Dict[str, Any]:
            return {
                "subjects": ["cat"], 
                "objects": ["fish"], 
                "root_verb": "eats",
                "noun_phrases": ["cat"],
                "verb_phrases": ["eats"],
                "entities": [],
                "dependency_types": ["nsubj", "ROOT", "dobj"],
                "token_count": 3,
                "complexity_score": 1.5
            }
        
        def is_available(self) -> bool:
            return False
        
        def get_model_info(self) -> Dict[str, Any]:
            return {
                "model_name": "fallback_rules",
                "spacy_version": None,
                "pipeline": ["basic_pos", "basic_deps"],
                "available": False
            }

class TestDependencyToken(unittest.TestCase):
    """Test DependencyToken data class"""
    
    def test_token_creation(self):
        """Test creating a dependency token"""
        token = DependencyToken(
            text="cat",
            lemma="cat",
            pos="NOUN",
            tag="NN",
            dep="nsubj",
            head_idx=1,
            children_idx=[]
        )
        
        self.assertEqual(token.text, "cat")
        self.assertEqual(token.lemma, "cat")
        self.assertEqual(token.pos, "NOUN")
        self.assertEqual(token.dep, "nsubj")
        self.assertEqual(token.head_idx, 1)
        self.assertFalse(token.is_root)
    
    def test_root_token(self):
        """Test creating a root token"""
        token = DependencyToken(
            text="runs",
            lemma="run",
            pos="VERB",
            tag="VBZ",
            dep="ROOT",
            head_idx=0,
            children_idx=[1, 2],
            is_root=True
        )
        
        self.assertTrue(token.is_root)
        self.assertEqual(token.dep, "ROOT")
        self.assertEqual(len(token.children_idx), 2)

class TestDependencyParse(unittest.TestCase):
    """Test DependencyParse data class"""
    
    def test_parse_creation(self):
        """Test creating a dependency parse"""
        tokens = [
            DependencyToken("cat", "cat", "NOUN", "NN", "nsubj", 1, []),
            DependencyToken("runs", "run", "VERB", "VBZ", "ROOT", 1, [0], True)
        ]
        
        parse = DependencyParse(
            tokens=tokens,
            root_idx=1,
            sentence="cat runs",
            entities=[],
            noun_phrases=["cat"],
            verb_phrases=["runs"],
            dependencies={"nsubj": [0], "ROOT": [1]}
        )
        
        self.assertEqual(len(parse.tokens), 2)
        self.assertEqual(parse.root_idx, 1)
        self.assertEqual(parse.sentence, "cat runs")
        self.assertIn("nsubj", parse.dependencies)
        self.assertIn("ROOT", parse.dependencies)

class TestDependencyParser(unittest.TestCase):
    """Test DependencyParser class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = DependencyParser()
    
    def test_parser_initialization(self):
        """Test parser initialization"""
        self.assertIsNotNone(self.parser)
        self.assertIsInstance(self.parser.model_name, str)
    
    def test_simple_sentence_parsing(self):
        """Test parsing a simple sentence"""
        text = "cat runs"
        parse = self.parser.parse(text)
        
        self.assertIsInstance(parse, DependencyParse)
        self.assertEqual(parse.sentence, text)
        self.assertGreater(len(parse.tokens), 0)
        self.assertGreaterEqual(parse.root_idx, 0)
    
    def test_svo_sentence_parsing(self):
        """Test parsing subject-verb-object sentence"""
        text = "cat eats fish"
        parse = self.parser.parse(text)
        
        self.assertEqual(parse.sentence, text)
        self.assertEqual(len(parse.tokens), 3)
        
        # Check that we have basic dependency structure
        self.assertIsInstance(parse.dependencies, dict)
        self.assertGreater(len(parse.dependencies), 0)
    
    def test_empty_text_error(self):
        """Test that empty text raises an error"""
        with self.assertRaises(Exception):  # ParseError or similar
            self.parser.parse("")
        
        with self.assertRaises(Exception):
            self.parser.parse("   ")
    
    def test_get_subjects(self):
        """Test extracting subjects from parse"""
        text = "cat runs"
        parse = self.parser.parse(text)
        subjects = self.parser.get_subjects(parse)
        
        self.assertIsInstance(subjects, list)
        # Should find at least one subject
        self.assertGreater(len(subjects), 0)
    
    def test_get_objects(self):
        """Test extracting objects from parse"""
        text = "cat eats fish"
        parse = self.parser.parse(text)
        objects = self.parser.get_objects(parse)
        
        self.assertIsInstance(objects, list)
        # Should find at least one object for SVO sentence
        if len(parse.tokens) >= 3:
            self.assertGreater(len(objects), 0)
    
    def test_get_root_verb(self):
        """Test extracting root verb"""
        text = "cat runs"
        parse = self.parser.parse(text)
        root_verb = self.parser.get_root_verb(parse)
        
        self.assertIsInstance(root_verb, (str, type(None)))
        if root_verb:
            self.assertIsInstance(root_verb, str)
    
    def test_analyze_sentence_structure(self):
        """Test sentence structure analysis"""
        text = "cat eats fish"
        parse = self.parser.parse(text)
        structure = self.parser.analyze_sentence_structure(parse)
        
        self.assertIsInstance(structure, dict)
        
        # Check expected keys
        expected_keys = ['subjects', 'objects', 'root_verb', 'noun_phrases', 
                        'verb_phrases', 'entities', 'dependency_types', 
                        'token_count', 'complexity_score']
        
        for key in expected_keys:
            self.assertIn(key, structure)
    
    def test_model_info(self):
        """Test getting model information"""
        info = self.parser.get_model_info()
        
        self.assertIsInstance(info, dict)
        self.assertIn('available', info)
        self.assertIn('model_name', info)
        self.assertIsInstance(info['available'], bool)
    
    def test_availability_check(self):
        """Test checking if SpaCy is available"""
        available = self.parser.is_available()
        self.assertIsInstance(available, bool)

class TestDependencyParserFallback(unittest.TestCase):
    """Test dependency parser fallback functionality"""
    
    def setUp(self):
        """Set up test with forced fallback mode"""
        # Force fallback mode by mocking SpaCy availability
        with patch('core.dependency_parser.SPACY_AVAILABLE', False):
            self.parser = DependencyParser()
    
    def test_fallback_parsing(self):
        """Test parsing with fallback rules"""
        text = "cat is mammal"
        parse = self.parser.parse(text)
        
        self.assertEqual(parse.sentence, text)
        self.assertGreater(len(parse.tokens), 0)
        self.assertIsInstance(parse.dependencies, dict)
    
    def test_fallback_pos_guessing(self):
        """Test POS guessing in fallback mode"""
        # This tests the internal _guess_pos method indirectly
        text = "cat runs quickly"
        parse = self.parser.parse(text)
        
        # Should have assigned some POS tags
        for token in parse.tokens:
            self.assertIsInstance(token.pos, str)
            self.assertGreater(len(token.pos), 0)
    
    def test_fallback_noun_phrases(self):
        """Test noun phrase extraction in fallback mode"""
        text = "big cat runs"
        parse = self.parser.parse(text)
        
        self.assertIsInstance(parse.noun_phrases, list)
        # Should extract some noun phrases
        self.assertGreater(len(parse.noun_phrases), 0)
    
    def test_fallback_verb_phrases(self):
        """Test verb phrase extraction in fallback mode"""
        text = "cat can run"
        parse = self.parser.parse(text)
        
        self.assertIsInstance(parse.verb_phrases, list)
        # Should extract some verb phrases
        self.assertGreater(len(parse.verb_phrases), 0)

class TestDependencyParserIntegration(unittest.TestCase):
    """Integration tests for dependency parser"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = DependencyParser()
    
    def test_complex_sentence(self):
        """Test parsing a more complex sentence"""
        text = "The big cat quickly runs to the house"
        parse = self.parser.parse(text)
        
        self.assertEqual(parse.sentence, text)
        self.assertGreater(len(parse.tokens), 5)
        
        # Should have multiple dependency types
        self.assertGreater(len(parse.dependencies), 1)
    
    def test_question_parsing(self):
        """Test parsing questions"""
        text = "What can cats do"
        parse = self.parser.parse(text)
        
        self.assertEqual(parse.sentence, text)
        self.assertGreater(len(parse.tokens), 0)
    
    def test_conditional_sentence(self):
        """Test parsing conditional sentences"""
        text = "If it rains then ground gets wet"
        parse = self.parser.parse(text)
        
        self.assertEqual(parse.sentence, text)
        self.assertGreater(len(parse.tokens), 5)
    
    def test_multiple_sentences(self):
        """Test parsing multiple different sentence types"""
        sentences = [
            "cat runs",
            "dog eats food",
            "birds can fly",
            "mammals cannot fly",
            "the quick brown fox jumps"
        ]
        
        for sentence in sentences:
            with self.subTest(sentence=sentence):
                parse = self.parser.parse(sentence)
                self.assertEqual(parse.sentence, sentence)
                self.assertGreater(len(parse.tokens), 0)
                
                # Analyze structure
                structure = self.parser.analyze_sentence_structure(parse)
                self.assertIsInstance(structure, dict)
                self.assertIn('complexity_score', structure)
                self.assertIsInstance(structure['complexity_score'], (int, float))

class TestDependencyParserPerformance(unittest.TestCase):
    """Performance tests for dependency parser"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = DependencyParser()
    
    def test_parsing_speed(self):
        """Test that parsing completes in reasonable time"""
        import time
        
        text = "The quick brown fox jumps over the lazy dog"
        
        start_time = time.time()
        parse = self.parser.parse(text)
        end_time = time.time()
        
        # Should complete in under 1 second (generous for fallback mode)
        self.assertLess(end_time - start_time, 1.0)
        self.assertIsInstance(parse, DependencyParse)
    
    def test_memory_usage(self):
        """Test that parsing doesn't consume excessive memory"""
        # Parse multiple sentences to check for memory leaks
        sentences = [f"sentence number {i} with some words" for i in range(100)]
        
        for sentence in sentences:
            parse = self.parser.parse(sentence)
            self.assertIsInstance(parse, DependencyParse)
            # Basic check that parse is valid
            self.assertGreater(len(parse.tokens), 0)

def run_all_tests():
    """Run all dependency parser tests"""
    print("🧪 Running Dependency Parser Tests")
    print("=" * 50)
    
    # Create test suite
    test_classes = [
        TestDependencyToken,
        TestDependencyParse,
        TestDependencyParser,
        TestDependencyParserFallback,
        TestDependencyParserIntegration,
        TestDependencyParserPerformance
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"📊 Test Summary:")
    print(f"✅ Tests run: {result.testsRun}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"⚠️  Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n⚠️  Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Error:')[-1].strip()}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n🎯 Success Rate: {success_rate:.1f}%")
    
    return result.wasSuccessful()

class InteractiveMode:
    """Interactive testing mode for dependency parser"""
    
    def __init__(self):
        self.parser = DependencyParser()
    
    def run(self):
        """Run interactive dependency parsing session"""
        print("🔍 Interactive Dependency Parser")
        print("=" * 40)
        print("Enter sentences to parse (type 'quit' to exit)")
        print(f"SpaCy available: {self.parser.is_available()}")
        print()
        
        while True:
            try:
                text = input("📝 Enter sentence: ").strip()
                
                if text.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not text:
                    continue
                
                # Parse sentence
                parse = self.parser.parse(text)
                
                # Display results
                print(f"\n🔍 Analysis of: '{text}'")
                print("-" * 30)
                
                # Basic structure
                structure = self.parser.analyze_sentence_structure(parse)
                print(f"📊 Subjects: {structure['subjects']}")
                print(f"📊 Root verb: {structure['root_verb']}")
                print(f"📊 Objects: {structure['objects']}")
                print(f"📊 Noun phrases: {structure['noun_phrases']}")
                print(f"📊 Verb phrases: {structure['verb_phrases']}")
                print(f"📊 Complexity: {structure['complexity_score']}")
                
                # Dependency details
                print(f"\n🔗 Dependencies:")
                for dep_type, indices in parse.dependencies.items():
                    tokens = [parse.tokens[i].text for i in indices if i < len(parse.tokens)]
                    if tokens:
                        print(f"  {dep_type}: {tokens}")
                
                # Token details
                print(f"\n🏷️  Tokens:")
                for i, token in enumerate(parse.tokens):
                    root_marker = " (ROOT)" if token.is_root else ""
                    print(f"  {i}: {token.text} [{token.pos}] -> {token.dep}{root_marker}")
                
                print()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                print()
        
        print("👋 Goodbye!")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        interactive = InteractiveMode()
        interactive.run()
    else:
        run_all_tests()