#!/usr/bin/env python3
"""
Semantic validator for the Human-Like Language Engine
"""

import time
from typing import Dict, Any, <PERSON>, Tuple, Optional
from knowledge_base import KnowledgeBase
from logger import logger, ValidationError
from config import config

class SemanticValidator:
    """Validates sentences for semantic correctness with hierarchy support"""
    
    def __init__(self, knowledge_base: KnowledgeBase):
        self.kb = knowledge_base
        self.validation_rules = {
            'check_word_existence': True,
            'check_subject_verb_compatibility': True,
            'check_verb_object_compatibility': True,
            'check_type_constraints': True,
            'strict_mode': config.get('system.debug_mode', False)
        }
        # Check if knowledge base has hierarchy support
        self.hierarchy_enabled = hasattr(knowledge_base, 'hierarchy_manager')
        if self.hierarchy_enabled:
            self.hierarchy_manager = knowledge_base.hierarchy_manager
        logger.debug("Semantic validator initialized", hierarchy_enabled=self.hierarchy_enabled)
    
    def validate(self, parsed_sentence: Dict[str, Any]) -> <PERSON><PERSON>[bool, str, Dict[str, Any]]:
        """Validate parsed sentence for semantic correctness"""
        start_time = time.time()
        
        try:
            # Extract components
            subject = parsed_sentence.get('subject')
            verb = parsed_sentence.get('verb')
            obj = parsed_sentence.get('object')
            pattern = parsed_sentence.get('pattern')
            
            validation_details = {
                'checks_performed': [],
                'warnings': [],
                'subject_info': None,
                'verb_info': None,
                'object_info': None,
                'hierarchy_info': {}
            }
            
            # Perform validation checks
            checks = [
                self._check_word_existence,
                self._check_subject_verb_compatibility,
                self._check_verb_object_compatibility,
                self._check_type_constraints
            ]
            
            for check in checks:
                try:
                    is_valid, reason, details = check(subject, verb, obj, pattern)
                    validation_details['checks_performed'].append(check.__name__)
                    
                    if not is_valid:
                        duration = time.time() - start_time
                        logger.log_validation_error(parsed_sentence.get('raw_text', ''), reason)
                        logger.log_performance('validation', duration, result='failed')
                        return False, reason, validation_details
                    
                    # Merge details
                    if isinstance(details, dict):
                        for key, value in details.items():
                            if key in validation_details and value:
                                validation_details[key] = value
                
                except Exception as e:
                    error_msg = f"Validation check failed: {check.__name__} - {str(e)}"
                    logger.error(error_msg, exception=e)
                    if self.validation_rules.get('strict_mode', False):
                        return False, error_msg, validation_details
                    else:
                        validation_details['warnings'].append(error_msg)
            
            # Additional hierarchy-aware validation if enabled
            if self.hierarchy_enabled and subject:
                try:
                    hierarchy_check = self._check_hierarchy_constraints(subject, verb, obj, pattern)
                    validation_details['hierarchy_info'] = hierarchy_check.get('hierarchy_info', {})
                    if hierarchy_check.get('warnings'):
                        validation_details['warnings'].extend(hierarchy_check['warnings'])
                except Exception as e:
                    logger.error(f"Hierarchy validation failed", exception=e)
                    validation_details['warnings'].append(f"Hierarchy check failed: {str(e)}")
            
            duration = time.time() - start_time
            logger.log_performance('validation', duration, result='passed')
            return True, "Valid sentence", validation_details
            
        except Exception as e:
            logger.error(f"Validation failed with exception", exception=e)
            return False, f"Validation error: {str(e)}", {}
    
    def _check_word_existence(self, subject: str, verb: str, obj: Optional[str], pattern: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Check if all words exist in knowledge base"""
        details = {'subject_info': None, 'verb_info': None, 'object_info': None}
        
        # Check subject
        if not self.kb.has_concept(subject):
            return False, f"Unknown subject: {subject}", details
        details['subject_info'] = self.kb.get_concept(subject)
        
        # Check verb
        if not self.kb.has_concept(verb):
            return False, f"Unknown verb: {verb}", details
        details['verb_info'] = self.kb.get_concept(verb)
        
        # Check object if present
        if obj:
            if not self.kb.has_concept(obj):
                return False, f"Unknown object: {obj}", details
            details['object_info'] = self.kb.get_concept(obj)
        
        return True, "All words exist", details
    
    def _check_subject_verb_compatibility(self, subject: str, verb: str, obj: Optional[str], pattern: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Check if subject can perform the verb action with hierarchy awareness"""
        subject_info = self.kb.get_concept(subject)
        verb_info = self.kb.get_concept(verb)
        
        if not subject_info or not verb_info:
            return True, "Skipping compatibility check (missing info)", {}
        
        # Check if subject has this action (including inherited actions)
        if self.hierarchy_enabled and hasattr(self.kb, 'get_all_actions_for_subject'):
            actions_data = self.kb.get_all_actions_for_subject(subject)
            all_actions = actions_data.get('all', [])
        else:
            all_actions = subject_info.get('actions', [])
        
        if verb not in all_actions:
            return False, f"{subject} cannot {verb}", {}
        
        # Check verb's subject type requirements
        required_subject_types = verb_info.get('requires_subject', [])
        if required_subject_types:
            subject_type = subject_info.get('type')
            if subject_type not in required_subject_types:
                # If hierarchy is enabled, check if subject type is compatible through inheritance
                if self.hierarchy_enabled and hasattr(self.hierarchy_manager, 'is_subtype_of'):
                    type_compatible = any(self.hierarchy_manager.is_subtype_of(subject_type, req_type) 
                                        for req_type in required_subject_types)
                    if not type_compatible:
                        return False, f"{verb} requires subject of type {required_subject_types}, got {subject_type}", {}
                else:
                    return False, f"{verb} requires subject of type {required_subject_types}, got {subject_type}", {}
        
        return True, "Subject-verb compatibility OK", {}
    
    def _check_verb_object_compatibility(self, subject: str, verb: str, obj: Optional[str], pattern: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Check if verb-object combination is valid"""
        verb_info = self.kb.get_concept(verb)
        
        if not verb_info:
            return True, "Skipping verb-object check (missing verb info)", {}
        
        required_object_types = verb_info.get('requires_object', [])
        
        # If verb requires no object
        if not required_object_types:
            if obj:
                # Some verbs might optionally take objects
                logger.debug(f"Verb '{verb}' doesn't typically require object but got '{obj}'")
            return True, "Verb-object compatibility OK", {}
        
        # If verb requires object but none provided
        if required_object_types and not obj:
            return False, f"{verb} requires an object of type {required_object_types}", {}
        
        # Check object type compatibility
        if obj:
            obj_info = self.kb.get_concept(obj)
            if obj_info:
                obj_type = obj_info.get('type')
                if obj_type not in required_object_types:
                    return False, f"{verb} requires object of type {required_object_types}, got {obj_type}", {}
        
        return True, "Verb-object compatibility OK", {}
    
    def _check_type_constraints(self, subject: str, verb: str, obj: Optional[str], pattern: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Check additional type-based constraints with hierarchy awareness"""
        subject_info = self.kb.get_concept(subject)
        
        if not subject_info:
            return True, "Skipping type constraints (missing subject info)", {}
        
        subject_type = subject_info.get('type')
        
        # Enhanced type constraints with hierarchy
        if self.hierarchy_enabled and hasattr(self.hierarchy_manager, 'is_subtype_of'):
            # Check if subject type allows actions based on hierarchy
            if verb not in ['is', 'has', 'can']:
                # Physical actions for non-physical entities
                if verb in ['move', 'walk', 'run', 'jump'] and not self.hierarchy_manager.is_subtype_of(subject_type, 'physical_entity'):
                    return False, f"Abstract entities like '{subject}' cannot perform physical actions", {}
                
                # Cognitive actions for non-agents
                if verb in ['think', 'decide', 'plan'] and not self.hierarchy_manager.is_subtype_of(subject_type, 'agent'):
                    return False, f"Non-agents like '{subject}' cannot perform cognitive actions", {}
                
                # Living actions for non-living things
                if verb in ['eat', 'sleep', 'breathe'] and not self.hierarchy_manager.is_subtype_of(subject_type, 'living_thing'):
                    return False, f"Non-living things like '{subject}' cannot perform living actions", {}
        
        # Basic type constraints (fallback)
        if subject_type == 'object':
            # Objects typically can't perform actions
            return False, f"Objects like '{subject}' cannot perform actions", {}
        
        if subject_type == 'food':
            # Food items typically can't perform actions
            return False, f"Food items like '{subject}' cannot perform actions", {}
        
        return True, "Type constraints OK", {}
    
    def validate_with_hierarchy(self, subject: str, verb: str, obj: Optional[str] = None) -> Dict[str, Any]:
        """Validate sentence components with hierarchy awareness"""
        # Create a parsed sentence structure for the existing validate method
        parsed_sentence = {
            'subject': subject,
            'verb': verb,
            'object': obj,
            'pattern': 'svo' if obj else 'sv',
            'raw_text': f"{subject} {verb}" + (f" {obj}" if obj else "")
        }
        
        # Use existing validate method
        is_valid, reason, details = self.validate(parsed_sentence)
        
        # Return in the format expected by the knowledge engine
        result = {
            'valid': is_valid,
            'error': reason if not is_valid else None,
            'details': details
        }
        
        # Add suggestions if validation failed
        if not is_valid:
            result['suggestions'] = self.get_validation_suggestions(subject, verb, obj)
        
        return result
    
    def validate_question_semantics(self, question_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate semantic correctness of questions"""
        try:
            question_type = question_data.get('type')
            subject = question_data.get('subject')
            
            if not subject:
                return False, "Question missing subject"
            
            # Check if subject exists for certain question types
            if question_type in ['what_can_do', 'what_is']:
                if not self.kb.has_concept(subject):
                    # This might be OK for learning scenarios
                    logger.debug(f"Question about unknown concept: {subject}")
            
            return True, "Question semantics OK"
            
        except Exception as e:
            logger.error(f"Question validation failed", exception=e)
            return False, f"Question validation error: {str(e)}"
    
    def get_validation_suggestions(self, subject: str, verb: str, obj: Optional[str]) -> List[str]:
        """Get suggestions for fixing validation errors"""
        suggestions = []
        
        try:
            # Check for similar words
            if not self.kb.has_concept(subject):
                similar_subjects = self._find_similar_concepts(subject, 'animal')
                if similar_subjects:
                    suggestions.append(f"Did you mean one of these subjects: {', '.join(similar_subjects)}?")
            
            if not self.kb.has_concept(verb):
                similar_verbs = self._find_similar_concepts(verb, 'action')
                if similar_verbs:
                    suggestions.append(f"Did you mean one of these verbs: {', '.join(similar_verbs)}?")
            
            if obj and not self.kb.has_concept(obj):
                similar_objects = self._find_similar_concepts(obj, 'object')
                if similar_objects:
                    suggestions.append(f"Did you mean one of these objects: {', '.join(similar_objects)}?")
            
            # Suggest valid actions for subject
            if self.kb.has_concept(subject):
                if hasattr(self.kb, 'get_all_actions_for_subject'):
                    actions_data = self.kb.get_all_actions_for_subject(subject)
                    valid_actions = actions_data.get('all', [])
                else:
                    subject_info = self.kb.get_concept(subject)
                    valid_actions = subject_info.get('actions', []) if subject_info else []
                if valid_actions and verb not in valid_actions:
                    suggestions.append(f"{subject} can: {', '.join(valid_actions)}")
            
        except Exception as e:
            logger.error(f"Failed to generate suggestions", exception=e)
        
        return suggestions
    
    def _find_similar_concepts(self, word: str, concept_type: str) -> List[str]:
        """Find concepts similar to the given word (simple implementation)"""
        try:
            # Get all concepts and filter by type
            all_concepts = []
            if hasattr(self.kb, 'knowledge'):
                for concept_name, concept_data in self.kb.knowledge.items():
                    if concept_data.get('type') == concept_type:
                        all_concepts.append(concept_name)
            
            # Simple similarity: starts with same letter or contains word
            similar = []
            word_lower = word.lower()
            
            for concept in all_concepts:
                if (concept.startswith(word_lower[0]) or 
                    word_lower in concept or 
                    concept in word_lower):
                    similar.append(concept)
            
            return similar[:3]  # Return max 3 suggestions
            
        except Exception as e:
            logger.error(f"Failed to find similar concepts", exception=e)
            return []
    
    def _check_hierarchy_constraints(self, subject: str, verb: str, obj: Optional[str], pattern: str) -> Dict[str, Any]:
        """Check hierarchy-specific semantic constraints"""
        result = {
            'warnings': [],
            'hierarchy_info': {}
        }
        
        if not self.hierarchy_enabled:
            return result
        
        subject_info = self.kb.get_concept(subject)
        if not subject_info:
            return result
        
        subject_type = subject_info.get('type', 'unknown')
        
        if verb == "can" and obj:
            # Check if action is already inherited
            inherited_actions = subject_info.get('inherited_actions', [])
            if obj in inherited_actions:
                result['hierarchy_info']['action_source'] = 'inherited'
            else:
                result['hierarchy_info']['action_source'] = 'direct'
        
        elif verb == "is" and obj:
            # Check type compatibility in hierarchy
            if hasattr(self.hierarchy_manager, 'is_valid_type') and self.hierarchy_manager.is_valid_type(obj):
                # Check if it's a valid type assignment
                if hasattr(self.hierarchy_manager, 'is_subtype_of'):
                    if self.hierarchy_manager.is_subtype_of(obj, subject_type):
                        result['hierarchy_info']['type_relationship'] = 'specialization'
                    elif self.hierarchy_manager.is_subtype_of(subject_type, obj):
                        result['hierarchy_info']['type_relationship'] = 'generalization'
                    else:
                        result['warnings'].append(f"Type change from {subject_type} to {obj} crosses hierarchy branches")
                        result['hierarchy_info']['type_relationship'] = 'cross_branch'
            else:
                # It's a property
                inherited_properties = subject_info.get('inherited_properties', [])
                if obj in inherited_properties:
                    result['hierarchy_info']['property_source'] = 'inherited'
                else:
                    result['hierarchy_info']['property_source'] = 'direct'
        
        return result
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation statistics"""
        stats = {
            'validation_rules': self.validation_rules,
            'knowledge_base_size': len(self.kb.knowledge),
            'hierarchy_enabled': self.hierarchy_enabled,
            'supported_checks': [
                'word_existence',
                'subject_verb_compatibility', 
                'verb_object_compatibility',
                'type_constraints'
            ]
        }
        
        if self.hierarchy_enabled:
            stats['supported_checks'].append('hierarchy_constraints')
        
        return stats
    
    def update_validation_rules(self, rules: Dict[str, bool]) -> None:
        """Update validation rule settings"""
        self.validation_rules.update(rules)
        logger.info(f"Updated validation rules", rules=rules)