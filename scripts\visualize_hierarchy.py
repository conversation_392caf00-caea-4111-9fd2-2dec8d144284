#!/usr/bin/env python3
"""
Hierarchy Visualization Tool
Displays the semantic hierarchy structure in a tree format
"""

import json
from pathlib import Path
from typing import Dict, List, Any

class HierarchyVisualizer:
    """Visualizes the semantic hierarchy in a tree format"""
    
    def __init__(self, schema_file: str = "config/hierarchy_schema.json"):
        self.schema_file = schema_file
        self.hierarchy = {}
        self.load_hierarchy()
    
    def load_hierarchy(self) -> None:
        """Load hierarchy schema from JSON file"""
        try:
            schema_path = Path(self.schema_file)
            if schema_path.exists():
                with open(schema_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.hierarchy = data.get('hierarchy', {})
            else:
                print(f"❌ Hierarchy schema file not found: {schema_path}")
        except Exception as e:
            print(f"❌ Failed to load hierarchy: {e}")
    
    def find_root_nodes(self) -> List[str]:
        """Find all root nodes (nodes without parents)"""
        roots = []
        for node_name, node_data in self.hierarchy.items():
            if not node_data.get('parent'):
                roots.append(node_name)
        return sorted(roots)
    
    def get_children(self, node_name: str) -> List[str]:
        """Get direct children of a node"""
        node_data = self.hierarchy.get(node_name, {})
        return sorted(node_data.get('children', []))
    
    def print_node_details(self, node_name: str, indent: str = "") -> None:
        """Print detailed information about a node"""
        if node_name not in self.hierarchy:
            return
        
        node_data = self.hierarchy[node_name]
        level = node_data.get('level', 0)
        description = node_data.get('description', 'No description')
        properties = node_data.get('inherited_properties', [])
        actions = node_data.get('inherited_actions', [])
        
        print(f"{indent}📋 {node_name} (Level {level})")
        print(f"{indent}   📝 {description}")
        
        if properties:
            print(f"{indent}   🏷️  Properties: {', '.join(properties)}")
        
        if actions:
            print(f"{indent}   ⚡ Actions: {', '.join(actions)}")
        
        print()
    
    def print_tree(self, node_name: str = None, indent: str = "", is_last: bool = True, show_details: bool = False) -> None:
        """Print hierarchy tree starting from a node"""
        if node_name is None:
            # Print all root nodes
            roots = self.find_root_nodes()
            print("🌳 Semantic Hierarchy Structure\n")
            
            for i, root in enumerate(roots):
                is_last_root = (i == len(roots) - 1)
                self.print_tree(root, "", is_last_root, show_details)
            return
        
        # Print current node
        connector = "└── " if is_last else "├── "
        node_data = self.hierarchy.get(node_name, {})
        level = node_data.get('level', 0)
        description = node_data.get('description', '')
        
        # Choose emoji based on node type
        emoji = self._get_node_emoji(node_name)
        
        print(f"{indent}{connector}{emoji} {node_name} (L{level})")
        
        if show_details and description:
            detail_indent = indent + ("    " if is_last else "│   ")
            print(f"{detail_indent}💭 {description}")
        
        # Print children
        children = self.get_children(node_name)
        child_indent = indent + ("    " if is_last else "│   ")
        
        for i, child in enumerate(children):
            is_last_child = (i == len(children) - 1)
            self.print_tree(child, child_indent, is_last_child, show_details)
    
    def _get_node_emoji(self, node_name: str) -> str:
        """Get appropriate emoji for node type"""
        emoji_map = {
            'physical_entity': '🌍',
            'abstract_entity': '💭',
            'agent': '🤖',
            'animal': '🐾',
            'plant': '🌱',
            'human': '👤',
            'mammal': '🐺',
            'bird': '🐦',
            'fish': '🐟',
            'reptile': '🦎',
            'cat': '🐱',
            'dog': '🐕',
            'elephant': '🐘',
            'eagle': '🦅',
            'salmon': '🐠',
            'snake': '🐍',
            'tree': '🌳',
            'flower': '🌸',
            'concept': '💡',
            'emotion': '😊',
            'relationship': '🔗',
            'action': '⚡',
            'property': '🏷️'
        }
        return emoji_map.get(node_name, '📦')
    
    def print_statistics(self) -> None:
        """Print hierarchy statistics"""
        total_nodes = len(self.hierarchy)
        roots = self.find_root_nodes()
        
        # Count nodes by level
        level_counts = {}
        max_level = 0
        
        for node_data in self.hierarchy.values():
            level = node_data.get('level', 0)
            level_counts[level] = level_counts.get(level, 0) + 1
            max_level = max(max_level, level)
        
        print("📊 Hierarchy Statistics")
        print(f"   Total nodes: {total_nodes}")
        print(f"   Root nodes: {len(roots)} ({', '.join(roots)})")
        print(f"   Maximum depth: {max_level}")
        print("   Nodes per level:")
        
        for level in sorted(level_counts.keys()):
            count = level_counts[level]
            print(f"     Level {level}: {count} nodes")
        print()
    
    def print_inheritance_info(self, node_name: str) -> None:
        """Print inheritance information for a specific node"""
        if node_name not in self.hierarchy:
            print(f"❌ Node '{node_name}' not found in hierarchy")
            return
        
        node_data = self.hierarchy[node_name]
        print(f"🔍 Inheritance Info for '{node_name}'")
        print(f"   Level: {node_data.get('level', 0)}")
        print(f"   Parent: {node_data.get('parent', 'None')}")
        
        children = node_data.get('children', [])
        if children:
            print(f"   Children: {', '.join(children)}")
        
        properties = node_data.get('inherited_properties', [])
        if properties:
            print(f"   Properties: {', '.join(properties)}")
        
        actions = node_data.get('inherited_actions', [])
        if actions:
            print(f"   Actions: {', '.join(actions)}")
        print()

def main():
    """Main function to demonstrate hierarchy visualization"""
    visualizer = HierarchyVisualizer()
    
    print("=" * 60)
    print("🌳 SEMANTIC HIERARCHY VISUALIZATION")
    print("=" * 60)
    print()
    
    # Print statistics
    visualizer.print_statistics()
    
    # Print full tree
    visualizer.print_tree(show_details=False)
    
    print("\n" + "=" * 60)
    print("📋 DETAILED VIEW (with descriptions)")
    print("=" * 60)
    print()
    
    # Print tree with details
    visualizer.print_tree(show_details=True)
    
    print("\n" + "=" * 60)
    print("🔍 SAMPLE INHERITANCE INFO")
    print("=" * 60)
    print()
    
    # Show inheritance for some key nodes
    sample_nodes = ['cat', 'bird', 'human', 'animal']
    for node in sample_nodes:
        if node in visualizer.hierarchy:
            visualizer.print_inheritance_info(node)

if __name__ == "__main__":
    main()