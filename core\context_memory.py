#!/usr/bin/env python3
"""
Context Memory System for Hierarchy-Aware Knowledge Engine

This module implements temporal reasoning and dialogue history management,
allowing the system to maintain context across conversations and refer
back to previous interactions.

Features:
- Dialogue history tracking
- Temporal reasoning with timestamps
- Context-aware query resolution
- Reference resolution (pronouns, "it", "that", etc.)
- Session management
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum


class ContextType(Enum):
    """Types of context entries"""
    STATEMENT = "statement"
    QUESTION = "question"
    ANSWER = "answer"
    LEARNING = "learning"
    ERROR = "error"


class ReferenceType(Enum):
    """Types of references that can be resolved"""
    PRONOUN = "pronoun"  # he, she, it, they
    DEMONSTRATIVE = "demonstrative"  # this, that, these, those
    DEFINITE_ARTICLE = "definite_article"  # the (when referring to previous entity)
    IMPLICIT = "implicit"  # implied subject from context


@dataclass
class ContextEntry:
    """Represents a single entry in the dialogue history"""
    timestamp: float
    context_type: ContextType
    content: str
    parsed_content: Optional[Dict[str, Any]] = None
    entities: List[str] = None  # Entities mentioned in this entry
    response: Optional[str] = None
    session_id: str = "default"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.entities is None:
            self.entities = []
        if self.metadata is None:
            self.metadata = {}
    
    def age_seconds(self) -> float:
        """Get age of this entry in seconds"""
        return time.time() - self.timestamp
    
    def age_minutes(self) -> float:
        """Get age of this entry in minutes"""
        return self.age_seconds() / 60
    
    def is_recent(self, seconds: float = 300) -> bool:
        """Check if entry is recent (default: 5 minutes)"""
        return self.age_seconds() <= seconds


@dataclass
class ResolvedReference:
    """Represents a resolved reference"""
    original_text: str
    resolved_entity: str
    reference_type: ReferenceType
    confidence: float
    source_entry: ContextEntry


class ContextMemory:
    """Manages dialogue history and temporal reasoning"""
    
    def __init__(self, max_entries: int = 1000, max_age_hours: float = 24.0):
        self.max_entries = max_entries
        self.max_age_hours = max_age_hours
        self.history: List[ContextEntry] = []
        self.sessions: Dict[str, List[int]] = {}  # session_id -> list of entry indices
        self.entity_mentions: Dict[str, List[int]] = {}  # entity -> list of entry indices
        
        # Reference resolution patterns
        self.pronoun_patterns = {
            'it': ['thing', 'object', 'concept'],
            'he': ['male', 'man', 'boy'],
            'she': ['female', 'woman', 'girl'],
            'they': ['plural', 'group'],
            'this': ['recent', 'current'],
            'that': ['previous', 'mentioned']
        }
    
    def add_entry(self, content: str, context_type: ContextType, 
                  session_id: str = "default", parsed_content: Optional[Dict] = None,
                  entities: Optional[List[str]] = None, response: Optional[str] = None,
                  metadata: Optional[Dict] = None) -> ContextEntry:
        """Add a new entry to the context history"""
        
        entry = ContextEntry(
            timestamp=time.time(),
            context_type=context_type,
            content=content,
            parsed_content=parsed_content,
            entities=entities or [],
            response=response,
            session_id=session_id,
            metadata=metadata or {}
        )
        
        # Add to history
        self.history.append(entry)
        entry_index = len(self.history) - 1
        
        # Update session tracking
        if session_id not in self.sessions:
            self.sessions[session_id] = []
        self.sessions[session_id].append(entry_index)
        
        # Update entity mentions
        for entity in entry.entities:
            if entity not in self.entity_mentions:
                self.entity_mentions[entity] = []
            self.entity_mentions[entity].append(entry_index)
        
        # Clean up old entries
        self._cleanup_old_entries()
        
        return entry
    
    def get_recent_entries(self, session_id: str = "default", 
                          limit: int = 10, max_age_minutes: float = 30) -> List[ContextEntry]:
        """Get recent entries from a session"""
        if session_id not in self.sessions:
            return []
        
        recent_entries = []
        current_time = time.time()
        max_age_seconds = max_age_minutes * 60
        
        # Get entries from most recent
        for entry_idx in reversed(self.sessions[session_id]):
            if len(recent_entries) >= limit:
                break
            
            entry = self.history[entry_idx]
            if current_time - entry.timestamp <= max_age_seconds:
                recent_entries.append(entry)
        
        return list(reversed(recent_entries))  # Return in chronological order
    
    def resolve_reference(self, text: str, session_id: str = "default") -> Optional[ResolvedReference]:
        """Attempt to resolve pronouns and references to previous entities"""
        text_lower = text.lower().strip()
        
        # Get recent context
        recent_entries = self.get_recent_entries(session_id, limit=5, max_age_minutes=10)
        
        if not recent_entries:
            return None
        
        # Try to resolve pronouns
        if text_lower in ['it', 'this', 'that']:
            return self._resolve_neutral_pronoun(text_lower, recent_entries)
        
        elif text_lower in ['he', 'him']:
            return self._resolve_gendered_pronoun(text_lower, recent_entries, 'male')
        
        elif text_lower in ['she', 'her']:
            return self._resolve_gendered_pronoun(text_lower, recent_entries, 'female')
        
        elif text_lower in ['they', 'them']:
            return self._resolve_plural_pronoun(text_lower, recent_entries)
        
        # Try to resolve "the X" where X was mentioned before
        elif text_lower.startswith('the '):
            return self._resolve_definite_article(text_lower, recent_entries)
        
        return None
    
    def _resolve_neutral_pronoun(self, pronoun: str, recent_entries: List[ContextEntry]) -> Optional[ResolvedReference]:
        """Resolve 'it', 'this', 'that' to most recent relevant entity"""
        for entry in reversed(recent_entries):
            if entry.entities:
                # Prefer the most recent entity mentioned
                entity = entry.entities[-1]
                confidence = 0.8 if pronoun == 'it' else 0.7
                
                return ResolvedReference(
                    original_text=pronoun,
                    resolved_entity=entity,
                    reference_type=ReferenceType.PRONOUN if pronoun == 'it' else ReferenceType.DEMONSTRATIVE,
                    confidence=confidence,
                    source_entry=entry
                )
        return None
    
    def _resolve_gendered_pronoun(self, pronoun: str, recent_entries: List[ContextEntry], gender: str) -> Optional[ResolvedReference]:
        """Resolve gendered pronouns to appropriate entities"""
        # This is a simplified implementation - in practice, you'd need
        # gender information in your knowledge base
        for entry in reversed(recent_entries):
            if entry.entities:
                # For now, just return the most recent entity
                # TODO: Add gender checking against knowledge base
                entity = entry.entities[-1]
                
                return ResolvedReference(
                    original_text=pronoun,
                    resolved_entity=entity,
                    reference_type=ReferenceType.PRONOUN,
                    confidence=0.6,  # Lower confidence without gender checking
                    source_entry=entry
                )
        return None
    
    def _resolve_plural_pronoun(self, pronoun: str, recent_entries: List[ContextEntry]) -> Optional[ResolvedReference]:
        """Resolve 'they', 'them' to plural entities or groups"""
        for entry in reversed(recent_entries):
            if len(entry.entities) > 1:
                # Multiple entities mentioned - could be "they"
                entities_str = " and ".join(entry.entities)
                
                return ResolvedReference(
                    original_text=pronoun,
                    resolved_entity=entities_str,
                    reference_type=ReferenceType.PRONOUN,
                    confidence=0.7,
                    source_entry=entry
                )
        return None
    
    def _resolve_definite_article(self, text: str, recent_entries: List[ContextEntry]) -> Optional[ResolvedReference]:
        """Resolve 'the X' references"""
        # Extract the noun after 'the'
        noun = text[4:]  # Remove 'the '
        
        for entry in reversed(recent_entries):
            for entity in entry.entities:
                if noun.lower() in entity.lower() or entity.lower() in noun.lower():
                    return ResolvedReference(
                        original_text=text,
                        resolved_entity=entity,
                        reference_type=ReferenceType.DEFINITE_ARTICLE,
                        confidence=0.8,
                        source_entry=entry
                    )
        return None
    
    def get_context_for_query(self, query: str, session_id: str = "default") -> Dict[str, Any]:
        """Get relevant context for processing a query"""
        recent_entries = self.get_recent_entries(session_id, limit=5)
        
        context = {
            'recent_entries': [asdict(entry) for entry in recent_entries],
            'recent_entities': [],
            'recent_topics': [],
            'session_length': len(self.sessions.get(session_id, [])),
            'resolved_references': []
        }
        
        # Extract recent entities and topics
        for entry in recent_entries:
            context['recent_entities'].extend(entry.entities)
        
        # Remove duplicates while preserving order
        seen = set()
        context['recent_entities'] = [x for x in context['recent_entities'] 
                                    if not (x in seen or seen.add(x))]
        
        # Try to resolve any references in the query
        words = query.split()
        for word in words:
            resolved = self.resolve_reference(word, session_id)
            if resolved:
                context['resolved_references'].append(asdict(resolved))
        
        return context
    
    def _cleanup_old_entries(self):
        """Remove entries that are too old or exceed max count"""
        current_time = time.time()
        max_age_seconds = self.max_age_hours * 3600
        
        # Remove entries that are too old
        self.history = [entry for entry in self.history 
                       if current_time - entry.timestamp <= max_age_seconds]
        
        # Remove excess entries (keep most recent)
        if len(self.history) > self.max_entries:
            excess = len(self.history) - self.max_entries
            self.history = self.history[excess:]
        
        # Rebuild indices
        self._rebuild_indices()
    
    def _rebuild_indices(self):
        """Rebuild session and entity indices after cleanup"""
        self.sessions.clear()
        self.entity_mentions.clear()
        
        for idx, entry in enumerate(self.history):
            # Rebuild session index
            if entry.session_id not in self.sessions:
                self.sessions[entry.session_id] = []
            self.sessions[entry.session_id].append(idx)
            
            # Rebuild entity mentions
            for entity in entry.entities:
                if entity not in self.entity_mentions:
                    self.entity_mentions[entity] = []
                self.entity_mentions[entity].append(idx)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get context memory statistics"""
        total_entries = len(self.history)
        active_sessions = len([s for s in self.sessions.values() if s])
        
        if total_entries > 0:
            oldest_entry = min(entry.timestamp for entry in self.history)
            newest_entry = max(entry.timestamp for entry in self.history)
            span_hours = (newest_entry - oldest_entry) / 3600
        else:
            span_hours = 0
        
        return {
            'total_entries': total_entries,
            'active_sessions': active_sessions,
            'unique_entities': len(self.entity_mentions),
            'span_hours': round(span_hours, 2),
            'memory_usage_mb': self._estimate_memory_usage(),
            'avg_entities_per_entry': round(sum(len(e.entities) for e in self.history) / max(total_entries, 1), 2)
        }
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB"""
        # Rough estimation based on string lengths and object overhead
        total_chars = sum(len(entry.content) + len(str(entry.parsed_content)) + 
                         sum(len(e) for e in entry.entities) for entry in self.history)
        # Assume ~2 bytes per character + object overhead
        estimated_bytes = total_chars * 2 + len(self.history) * 200  # 200 bytes overhead per entry
        return round(estimated_bytes / (1024 * 1024), 3)
    
    def save_to_file(self, filepath: str):
        """Save context memory to file"""
        data = {
            'history': [asdict(entry) for entry in self.history],
            'sessions': self.sessions,
            'entity_mentions': self.entity_mentions,
            'metadata': {
                'max_entries': self.max_entries,
                'max_age_hours': self.max_age_hours,
                'saved_at': time.time()
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_from_file(self, filepath: str):
        """Load context memory from file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Restore history
            self.history = []
            for entry_data in data.get('history', []):
                entry = ContextEntry(
                    timestamp=entry_data['timestamp'],
                    context_type=ContextType(entry_data['context_type']),
                    content=entry_data['content'],
                    parsed_content=entry_data.get('parsed_content'),
                    entities=entry_data.get('entities', []),
                    response=entry_data.get('response'),
                    session_id=entry_data.get('session_id', 'default'),
                    metadata=entry_data.get('metadata', {})
                )
                self.history.append(entry)
            
            # Restore indices
            self.sessions = data.get('sessions', {})
            self.entity_mentions = data.get('entity_mentions', {})
            
            # Update settings if provided
            metadata = data.get('metadata', {})
            if 'max_entries' in metadata:
                self.max_entries = metadata['max_entries']
            if 'max_age_hours' in metadata:
                self.max_age_hours = metadata['max_age_hours']
            
            # Clean up old entries
            self._cleanup_old_entries()
            
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            # If file doesn't exist or is corrupted, start fresh
            self.history = []
            self.sessions = {}
            self.entity_mentions = {}
    
    def clear_session(self, session_id: str):
        """Clear all entries for a specific session"""
        if session_id in self.sessions:
            # Remove entries for this session
            session_indices = set(self.sessions[session_id])
            self.history = [entry for i, entry in enumerate(self.history) 
                          if i not in session_indices]
            
            # Remove session
            del self.sessions[session_id]
            
            # Rebuild indices
            self._rebuild_indices()
    
    def clear_all(self):
        """Clear all context memory"""
        self.history.clear()
        self.sessions.clear()
        self.entity_mentions.clear()


if __name__ == "__main__":
    # Example usage
    context = ContextMemory()
    
    # Add some entries
    context.add_entry(
        "The cat is sleeping", 
        ContextType.STATEMENT, 
        entities=["cat"],
        parsed_content={"subject": "cat", "action": "sleeping"}
    )
    
    context.add_entry(
        "What can it do?", 
        ContextType.QUESTION,
        response="The cat can sleep, meow, and chase mice."
    )
    
    # Test reference resolution
    resolved = context.resolve_reference("it")
    if resolved:
        print(f"Resolved 'it' to: {resolved.resolved_entity}")
    
    # Get context for a new query
    query_context = context.get_context_for_query("Is it awake?")
    print(f"Context: {query_context}")
    
    # Show statistics
    stats = context.get_statistics()
    print(f"Statistics: {stats}")