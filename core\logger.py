#!/usr/bin/env python3
"""
Logging system for the Human-Like Language Engine
"""

import logging
import os
from datetime import datetime
from typing import Optional
from config import config

class EngineLogger:
    """Custom logger for the language engine"""
    
    def __init__(self, name: str = "LanguageEngine"):
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self) -> None:
        """Configure logger based on config settings"""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        log_level = getattr(logging, config.get('logging.level', 'INFO').upper())
        self.logger.setLevel(log_level)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler
        if config.get('logging.console_output', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # File handler
        log_file = config.get('logging.file_path', 'engine.log')
        if log_file:
            # Create logs directory if it doesn't exist
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message"""
        self.logger.debug(self._format_message(message, **kwargs))
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message"""
        self.logger.info(self._format_message(message, **kwargs))
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message"""
        self.logger.warning(self._format_message(message, **kwargs))
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs) -> None:
        """Log error message with optional exception"""
        formatted_message = self._format_message(message, **kwargs)
        if exception:
            self.logger.error(f"{formatted_message} - Exception: {str(exception)}")
        else:
            self.logger.error(formatted_message)
    
    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs) -> None:
        """Log critical message"""
        formatted_message = self._format_message(message, **kwargs)
        if exception:
            self.logger.critical(f"{formatted_message} - Exception: {str(exception)}")
        else:
            self.logger.critical(formatted_message)
    
    def _format_message(self, message: str, **kwargs) -> str:
        """Format message with additional context"""
        if kwargs:
            context = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            return f"{message} [{context}]"
        return message
    
    def log_performance(self, operation: str, duration: float, **kwargs) -> None:
        """Log performance metrics"""
        self.info(f"Performance: {operation} took {duration:.4f}s", **kwargs)
    
    def log_learning_event(self, subject: str, fact_type: str, fact_value: str) -> None:
        """Log learning events"""
        self.info(f"Learning: {subject} learned {fact_type}={fact_value}")
    
    def log_validation_error(self, sentence: str, reason: str) -> None:
        """Log sentence validation errors"""
        self.debug(f"Validation failed: '{sentence}' - {reason}")
    
    def log_question_answered(self, question: str, answer: str) -> None:
        """Log question-answer pairs"""
        self.debug(f"Q&A: '{question}' -> '{answer}'")

# Global logger instance
logger = EngineLogger()

# Custom exceptions for better error handling
class EngineError(Exception):
    """Base exception for engine errors"""
    pass

class ParseError(EngineError):
    """Exception for parsing errors"""
    pass

class ValidationError(EngineError):
    """Exception for validation errors"""
    pass

class KnowledgeError(EngineError):
    """Exception for knowledge base errors"""
    pass

class ConfigError(EngineError):
    """Exception for configuration errors"""
    pass