#!/usr/bin/env python3
"""
Simple Logger Module
Provides basic logging functionality and custom exceptions
"""

import logging
import sys
from typing import Any

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Create logger instance
logger = logging.getLogger('english_llm')

# Custom exceptions
class ParseError(Exception):
    """Exception raised for parsing errors"""
    pass

class KnowledgeError(Exception):
    """Exception raised for knowledge base errors"""
    pass

class ReasoningError(Exception):
    """Exception raised for reasoning errors"""
    pass

# Utility functions
def log_info(message: str) -> None:
    """Log an info message"""
    logger.info(message)

def log_error(message: str, exception: Exception = None) -> None:
    """Log an error message"""
    if exception:
        logger.error(f"{message}: {str(exception)}")
    else:
        logger.error(message)

def log_warning(message: str) -> None:
    """Log a warning message"""
    logger.warning(message)

def log_debug(message: str) -> None:
    """Log a debug message"""
    logger.debug(message)