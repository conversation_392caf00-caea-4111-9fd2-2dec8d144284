{"hierarchy": {"entity": {"level": 0, "parent": null, "children": ["physical_entity", "abstract_entity", "agent"], "description": "The root of all existence - anything that can be conceived or exist", "inherited_properties": ["exists", "can_be_referenced"], "inherited_actions": []}, "physical_entity": {"level": 1, "parent": "entity", "children": ["living_thing", "non_living_thing"], "description": "Exists in space and/or time with physical presence", "inherited_properties": ["has_mass", "occupies_space", "exists_in_time"], "inherited_actions": ["can_be_moved", "can_be_measured"]}, "living_thing": {"level": 2, "parent": "physical_entity", "children": ["animal", "plant"], "description": "Biological entities with life processes", "inherited_properties": ["alive", "grows", "reproduces", "needs_energy", "responds_to_environment"], "inherited_actions": ["breathe", "grow", "reproduce", "die"]}, "animal": {"level": 3, "parent": "living_thing", "children": ["mammal", "bird", "fish", "reptile"], "description": "Living things that can move and typically consume other organisms", "inherited_properties": ["mobile", "consumes_food", "has_nervous_system", "senses_environment"], "inherited_actions": ["move", "eat", "sleep", "sense"]}, "mammal": {"level": 4, "parent": "animal", "children": [], "description": "Warm-blooded animals that typically have hair and feed milk to young", "inherited_properties": ["warm_blooded", "has_hair_or_fur", "produces_milk", "gives_live_birth"], "inherited_actions": ["nurse_young", "regulate_body_temperature"]}, "bird": {"level": 4, "parent": "animal", "children": [], "description": "Warm-blooded animals with feathers, wings, and beaks", "inherited_properties": ["has_feathers", "has_wings", "has_beak", "lays_eggs"], "inherited_actions": ["fly", "build_nest", "lay_eggs"]}, "fish": {"level": 4, "parent": "animal", "children": [], "description": "Aquatic animals with gills and fins", "inherited_properties": ["has_gills", "has_fins", "lives_in_water", "cold_blooded"], "inherited_actions": ["swim", "breathe_underwater"]}, "reptile": {"level": 4, "parent": "animal", "children": [], "description": "Cold-blooded animals with scales", "inherited_properties": ["has_scales", "cold_blooded", "lays_eggs"], "inherited_actions": ["shed_skin", "bask_in_sun"]}, "plant": {"level": 3, "parent": "living_thing", "children": [], "description": "Living organisms that typically photosynthesize", "inherited_properties": ["photosynthesizes", "has_roots", "stationary", "produces_oxygen"], "inherited_actions": ["photosynthesize", "absorb_nutrients", "grow_towards_light"]}, "non_living_thing": {"level": 2, "parent": "physical_entity", "children": ["object", "material", "location"], "description": "Physical entities without life processes", "inherited_properties": ["inanimate", "does_not_grow", "does_not_reproduce"], "inherited_actions": []}, "object": {"level": 3, "parent": "non_living_thing", "children": [], "description": "Discrete physical items - made, found, or naturally occurring", "inherited_properties": ["has_shape", "has_boundaries", "can_be_manipulated"], "inherited_actions": ["can_be_used", "can_be_broken"]}, "material": {"level": 3, "parent": "non_living_thing", "children": [], "description": "Substances and matter in various states", "inherited_properties": ["has_chemical_composition", "has_physical_state"], "inherited_actions": ["can_change_state", "can_react"]}, "location": {"level": 3, "parent": "non_living_thing", "children": ["planet", "continent"], "description": "Places and positions in space", "inherited_properties": ["has_coordinates", "has_boundaries", "contains_other_entities"], "inherited_actions": ["can_be_visited", "can_contain"]}, "planet": {"level": 4, "parent": "location", "children": [], "description": "A celestial body moving in an elliptical orbit around a star", "inherited_properties": ["has_gravity", "has_atmosphere", "orbits_star"], "inherited_actions": ["can_be_orbited", "can_sustain_life"]}, "continent": {"level": 4, "parent": "location", "children": ["country"], "description": "Any of the world's main continuous expanses of land", "inherited_properties": ["is_landmass", "has_countries"], "inherited_actions": ["can_be_traveled_across"]}, "country": {"level": 5, "parent": "continent", "children": [], "description": "A nation with its own government, occupying a particular territory", "inherited_properties": ["has_government", "has_population", "has_capital_city"], "inherited_actions": ["can_declare_war", "can_sign_treaties"]}, "abstract_entity": {"level": 1, "parent": "entity", "children": ["concept", "quantity", "property", "event", "time", "relation"], "description": "Exists only in mind, thought, or as concepts", "inherited_properties": ["intangible", "conceptual", "exists_in_mind"], "inherited_actions": ["can_be_thought_about", "can_be_communicated"]}, "concept": {"level": 2, "parent": "abstract_entity", "children": [], "description": "General ideas and mental constructs", "inherited_properties": ["abstract", "can_be_defined", "can_be_learned"], "inherited_actions": ["can_be_understood", "can_be_taught"]}, "quantity": {"level": 2, "parent": "abstract_entity", "children": [], "description": "Numbers, measures, and amounts", "inherited_properties": ["measurable", "comparable", "mathematical"], "inherited_actions": ["can_be_calculated", "can_be_compared"]}, "property": {"level": 2, "parent": "abstract_entity", "children": [], "description": "Traits, qualities, and characteristics", "inherited_properties": ["descriptive", "attributable", "observable"], "inherited_actions": ["can_be_observed", "can_be_measured"]}, "event": {"level": 2, "parent": "abstract_entity", "children": [], "description": "Happenings and occurrences in time", "inherited_properties": ["temporal", "has_duration", "has_participants"], "inherited_actions": ["can_happen", "can_be_planned"]}, "time": {"level": 2, "parent": "abstract_entity", "children": [], "description": "Past, present, future points and intervals", "inherited_properties": ["temporal", "sequential", "measurable"], "inherited_actions": ["can_pass", "can_be_measured"]}, "relation": {"level": 2, "parent": "abstract_entity", "children": [], "description": "Connections and relationships between entities", "inherited_properties": ["connects_entities", "has_direction", "can_be_symmetric"], "inherited_actions": ["can_be_established", "can_be_broken"]}, "agent": {"level": 1, "parent": "entity", "children": ["human", "intelligent_system"], "description": "Entities capable of intentional action and decision-making", "inherited_properties": ["has_agency", "can_make_decisions", "has_intentions", "can_plan"], "inherited_actions": ["decide", "plan", "act_intentionally", "learn"]}, "human": {"level": 2, "parent": "agent", "children": [], "description": "Homo sapiens - intelligent social mammals", "inherited_properties": ["rational", "social", "uses_language", "creates_culture"], "inherited_actions": ["speak", "think", "create", "cooperate"]}, "intelligent_system": {"level": 2, "parent": "agent", "children": [], "description": "AI, gods, fictional beings with intelligence", "inherited_properties": ["artificial_or_supernatural", "processes_information", "exhibits_intelligence"], "inherited_actions": ["process_information", "make_decisions", "interact"]}}, "metadata": {"version": "1.0", "created": "2025-01-15", "description": "Hierarchical semantic taxonomy for deep knowledge representation", "max_depth": 5, "total_types": 24}}