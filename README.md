# 🧠 English LLM - Advanced Natural Language Processing System

A comprehensive natural language processing system with enhanced parsing capabilities, dependency analysis, context memory, reasoning chains, and conditional logic processing.

## ✨ Features

### 🔍 **Enhanced Parsing**
- **Complex Sentence Analysis**: Handles simple, compound, complex, conditional, and relative sentences
- **Clause Extraction**: Identifies main and subordinate clauses
- **Grammatical Structure**: Extracts subjects, verbs, objects, and modifiers
- **Complexity Scoring**: Provides sentence complexity metrics

### 🔗 **Dependency Parsing**
- **SpaCy Integration**: Uses advanced NLP models for grammatical analysis
- **Token Relationships**: Identifies dependencies between words
- **Phrase Extraction**: Extracts noun and verb phrases
- **Fallback Mode**: Works even without SpaCy installation

### 🧠 **Context Memory**
- **Session Tracking**: Maintains conversation context
- **Entry Classification**: Categorizes different types of inputs
- **History Management**: Stores and retrieves previous interactions
- **Context-Aware Processing**: Uses historical data for better understanding

### 💭 **Reasoning Chains**
- **Symbolic Reasoning**: Performs logical inference and deduction
- **Multi-Step Analysis**: Breaks down complex reasoning into steps
- **Evidence Tracking**: Maintains proof chains and confidence scores
- **Knowledge Integration**: Works with hierarchical knowledge structures

### ⚡ **Conditional Logic**
- **If-Then Processing**: Handles conditional statements and rules
- **Rule Engine**: Manages logical rules and facts
- **Inference System**: Derives new conclusions from existing knowledge
- **Explanation Generation**: Provides human-readable reasoning explanations

### 🌐 **Web Interface**
- **Interactive Testing**: Beautiful web UI for testing all features
- **Real-Time Analysis**: Instant processing and results display
- **Component Status**: Live monitoring of system components
- **Session History**: Track and review previous analyses

## 🚀 Quick Start

### Prerequisites
```bash
pip install flask spacy
python -m spacy download en_core_web_sm
```

### Running the Web Interface
```bash
python web_interface.py
```

Then open your browser to `http://localhost:5000`

### Command Line Usage
```python
from core.enhanced_parser import EnhancedSentenceParser
from core.dependency_parser import DependencyParser

# Enhanced parsing
parser = EnhancedSentenceParser()
result = parser.parse("If it rains tomorrow, I will stay home.")
print(f"Sentence type: {result.sentence_type}")
print(f"Main clause: {result.main_clause}")

# Dependency parsing
dep_parser = DependencyParser()
dep_result = dep_parser.parse("The cat that sits on the mat is sleeping.")
structure = dep_parser.analyze_sentence_structure(dep_result)
print(f"Subjects: {structure['subjects']}")
print(f"Objects: {structure['objects']}")
```

## 📁 Project Structure

```
english_llm/
├── core/                          # Core processing modules
│   ├── enhanced_parser.py         # Complex sentence parsing
│   ├── dependency_parser.py       # SpaCy-based dependency analysis
│   ├── context_memory.py          # Context and session management
│   ├── reasoning_chains.py        # Symbolic reasoning engine
│   ├── conditional_logic.py       # Conditional logic processing
│   └── __init__.py               # Module initialization
├── tests/                         # Comprehensive test suite
│   ├── test_enhanced_parser.py    # Enhanced parser tests
│   ├── test_dependency_parser.py  # Dependency parser tests
│   ├── test_context_memory.py     # Context memory tests
│   ├── test_reasoning_chains.py   # Reasoning engine tests
│   └── test_conditional_logic.py  # Conditional logic tests
├── templates/                     # Web interface templates
│   └── index.html                # Main web interface
├── web_interface.py              # Flask web application
├── logger.py                     # Centralized logging
├── config.py                     # Configuration management
├── plan.md                       # Development roadmap
└── README.md                     # This file
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test individual components
python -m pytest tests/test_enhanced_parser.py -v
python -m pytest tests/test_dependency_parser.py -v
python -m pytest tests/test_context_memory.py -v
python -m pytest tests/test_reasoning_chains.py -v
python -m pytest tests/test_conditional_logic.py -v

# Run all tests
python -m pytest tests/ -v
```

Or use the built-in test runners:

```python
# Enhanced parser tests
from tests.test_enhanced_parser import run_all_tests
run_all_tests()

# Dependency parser tests
from tests.test_dependency_parser import run_all_tests
run_all_tests()
```

## 📊 Example Analyses

### Complex Sentence Parsing
```python
sentence = "The cat that sits on the mat is sleeping peacefully."
result = parser.parse(sentence)
# Output: SentenceType.RELATIVE with extracted clauses
```

### Conditional Logic
```python
sentence = "If it rains tomorrow, then I will stay home."
result = parser.parse(sentence)
# Output: Conditional structure with condition and consequence
```

### Dependency Analysis
```python
sentence = "John loves Mary, but she prefers Tom."
structure = dep_parser.analyze_sentence_structure(dep_parser.parse(sentence))
# Output: Subjects, objects, relationships, and complexity metrics
```

## 🔧 Configuration

The system uses `config.py` for configuration management:

```python
config = {
    'system': {
        'max_sentence_length': 20,
        'enable_spacy': True,
        'log_level': 'INFO'
    },
    'parsing': {
        'complexity_threshold': 0.7,
        'confidence_threshold': 0.5
    }
}
```

## 🎯 Web Interface Features

- **📝 Text Input**: Large text area for sentence input
- **🔍 Analysis Options**: Choose specific analysis types or run all
- **📊 Real-Time Results**: Instant JSON-formatted results
- **🧠 Component Status**: Live status indicators for all modules
- **📚 Session History**: Track and review previous analyses
- **🎨 Modern UI**: Beautiful, responsive design with gradients and animations

## 🔮 Future Enhancements

- **🌐 API Endpoints**: RESTful API for programmatic access
- **🤖 ML Integration**: BERT and transformer model integration
- **🔍 Semantic Search**: Advanced semantic similarity and search
- **📈 Rule Induction**: Automatic rule learning from examples
- **🗃️ Knowledge Graphs**: Enhanced knowledge representation
- **🌍 Multi-language**: Support for additional languages

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **SpaCy**: For advanced NLP capabilities
- **Flask**: For the web interface framework
- **Python**: For the robust development environment

---

**🚀 Ready to explore advanced natural language processing? Start with the web interface at `http://localhost:5000`!**