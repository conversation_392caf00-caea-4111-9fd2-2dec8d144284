#!/usr/bin/env python3
"""
Setup script for the Hierarchy-Aware Knowledge Engine.
"""

from setuptools import setup, find_packages
import os

# Read the README file
with open("docs/README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("config/requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="hierarchy-aware-knowledge-engine",
    version="1.0.0",
    author="AI Assistant",
    description="A hierarchy-aware knowledge engine for natural language processing",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "knowledge-engine=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "config": ["*.json"],
        "data": ["*.json"],
    },
)