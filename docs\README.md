# Hierarchy-Aware Knowledge Engine

🧠 A sophisticated natural language processing system that understands semantic hierarchies and relationships through symbolic reasoning.

## 🌟 Features

- **Hierarchical Knowledge Representation** - 4-level semantic taxonomy (entity → physical/abstract/agent → specialized types)
- **Intelligent Sentence Validation** - Type-aware semantic checking using inheritance
- **Smart Question Answering** - Context-aware responses with hierarchy information
- **Learning Capabilities** - Dynamic fact acquisition and knowledge expansion
- **Performance Optimized** - Caching and efficient algorithms for real-time processing

## 🚀 Development Roadmap

### Phase 1: Core Logic (✅ Current)
- ✅ Basic knowledge representation
- ✅ Simple sentence parsing
- ✅ Logical validation
- ✅ Question answering
- ✅ Basic learning

### Phase 2: Enhanced Reasoning
- 🔄 Grammar rule learning
- 🔄 Inference chains ("if A then B")
- 🔄 Contradiction detection
- 🔄 Analogical reasoning

### Phase 3: Expanded Language
- 📋 Adjectives and properties
- 📋 Tense and time
- 📋 Negation ("cat does not bark")
- 📋 Quantifiers ("all cats", "some dogs")

### Phase 4: Advanced Features
- 📋 Context and memory
- 📋 Dialogue management
- 📋 Explanation generation
- 📋 Self-reflection

## 🧠 Key Insights

- **Symbolic > Statistical** - Explicit rules beat pattern matching
- **Quality > Quantity** - 20K deep concepts > 1B shallow tokens
- **Logic > Prediction** - Understanding meaning > guessing words
- **Human-like > Machine-like** - Learn through stages, not brute force

## 🏗️ Architecture

```
core/
├── knowledge_engine_v3.py    # Main orchestrator
├── knowledge_base_v3.py       # Hierarchical knowledge storage
├── hierarchy_manager.py       # Semantic hierarchy management
├── parser.py                  # Sentence and question parsing
├── validator.py               # Semantic validation
└── logger.py                  # Logging system

config/
├── hierarchy_schema.json      # 4-level semantic taxonomy
├── config.json               # Engine configuration
└── requirements.txt          # Dependencies

data/
├── knowledge_base.json       # Knowledge storage
└── engine.log               # Runtime logs

tests/
├── test_engine_v2.py         # Comprehensive test suite
├── debug_hierarchy.py        # Debugging utilities
└── test_hierarchy_engine.py  # Hierarchy-specific tests
```

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r config/requirements.txt

# Run the interactive engine
python main.py

# Or install as package
python setup.py install
```

### Usage Examples

```python
from core import HierarchyAwareKnowledgeEngine

# Initialize engine
engine = HierarchyAwareKnowledgeEngine()

# Validate sentences
result = engine.validate_sentence("cat chase mouse")
print(result['valid'])  # True

# Answer questions
response = engine.answer_question("what can cat do?")
print(response['answer'])  # "cat can chase, sleep, eat..."

# Learn new facts
engine.process_sentence("dog bark loudly")
```

## 🧪 Testing

```bash
# Run full test suite
python tests/test_engine_v2.py

# Debug hierarchy relationships
python tests/debug_hierarchy.py

# Test specific components
pytest tests/ -v
```

## 📊 Hierarchy Structure

**Level 0:** `entity` (root)
**Level 1:** `physical_entity`, `abstract_entity`, `agent`
**Level 2:** `living_thing`, `object`, `concept`, `human`
**Level 3:** `animal`, `plant`, `material`, `location`
**Level 4:** `mammal`, `bird`, `fish`, `reptile`

### Inheritance Example

```
cat (animal)
├── Inherits from: animal → living_thing → physical_entity → entity
├── Properties: mobile, consumes_food, alive, exists, has_mass
├── Actions: move, eat, sleep, sense, breathe
└── Direct: chase, meow, jump
```

## 🔧 Technical Details

- **Language** - Pure Python (minimal external dependencies)
- **Architecture** - Modular, symbolic reasoning
- **Knowledge** - Dictionary-based with structured facts
- **Parsing** - Grammar pattern matching (evolved from simple splitting)
- **Validation** - Rule-based logical checking with hierarchy

### Engine Settings (`config/config.json`)

```json
{
  "knowledge_base_file": "data/knowledge_base.json",
  "hierarchy_schema_file": "config/hierarchy_schema.json",
  "logging_level": "INFO",
  "performance_tracking": true
}
```

### Adding New Concepts

```json
{
  "elephant": {
    "type": "mammal",
    "actions": ["trumpet", "spray_water"],
    "properties": ["large", "intelligent"],
    "meaning": "large mammal with trunk"
  }
}
```

## 🎯 Success Metrics

- ✅ Can validate 100+ sentence types correctly
- ✅ Can answer questions about any known concept
- ✅ Can learn new facts and apply them immediately
- ✅ Can explain why sentences are valid/invalid
- ✅ Can generalize rules to new examples

## 📈 Performance

- **Sentence Validation:** ~0.001-0.005s
- **Question Answering:** ~0.002-0.010s
- **Knowledge Learning:** ~0.001-0.003s
- **Test Success Rate:** 100% (25/25 tests)

## 🛠️ Development

### Project Structure

- `core/` - Main engine components
- `config/` - Configuration and schemas
- `data/` - Knowledge and logs
- `tests/` - Test suite and debugging
- `docs/` - Documentation
- `scripts/` - Utility scripts

### Key Components

1. **Knowledge Engine** - Orchestrates all processing
2. **Hierarchy Manager** - Manages semantic relationships
3. **Parser** - Extracts grammatical structure
4. **Validator** - Ensures semantic correctness
5. **Knowledge Base** - Stores and retrieves facts

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Run tests (`python tests/test_engine_v2.py`)
4. Commit changes (`git commit -m 'Add amazing feature'`)
5. Push to branch (`git push origin feature/amazing-feature`)
6. Open Pull Request

## 📝 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by semantic web technologies
- Built with modern Python best practices
- Designed for extensibility and performance

---

**Made with ❤️ for intelligent language understanding**

python web_interface.py