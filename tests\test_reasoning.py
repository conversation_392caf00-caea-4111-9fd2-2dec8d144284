#!/usr/bin/env python3
"""
Test script to analyze current logical reasoning capabilities
"""

from core.reasoning_chains import ReasoningChainEngine
from core.hierarchy_manager import HierarchyManager
from core.knowledge_base_v3 import HierarchyAwareKnowledgeBase

def test_logical_reasoning():
    """Test the current logical reasoning capabilities"""

    # Initialize components properly
    print("Initializing components...")
    hierarchy_manager = HierarchyManager('config/hierarchy_schema.json')
    knowledge_base = HierarchyAwareKnowledgeBase('data/knowledge_base.json', 'config/hierarchy_schema.json')
    reasoning_engine = ReasoningChainEngine(knowledge_base, hierarchy_manager)

    # Test logical reasoning questions
    questions = [
        'can cat breathe?',
        'can cat fly?',
        'can mammal breathe?',
        'can bird fly?',
        'can fish swim?'
    ]

    print('\n=== Testing Fixed Logical Reasoning ===')
    for question in questions:
        print(f'\nQ: {question}')
        result = reasoning_engine.reason(question)
        print(f'Answer: {"Yes" if result.final_answer else "No"}')
        print(f'Confidence: {result.overall_confidence:.2f}')
        print(f'Steps: {len(result.steps)}')
        print(f'Explanation: {result.explanation}')

        # Show reasoning steps
        for i, step in enumerate(result.steps, 1):
            print(f'  Step {i}: {step.explanation}')
            if step.evidence:
                print(f'    Evidence: {step.evidence}')

    # Test specific inheritance logic
    print('\n=== Testing Inheritance Logic ===')

    # Check if cat can breathe (should inherit from living_thing)
    cat_concept = knowledge_base.get_concept('cat')
    if cat_concept:
        print(f"Cat type: {cat_concept.get('type')}")
        print(f"Cat hierarchy path: {cat_concept.get('hierarchy_path', [])}")
        print(f"Cat inherited actions: {cat_concept.get('inherited_actions', [])}")

    # Test hierarchy manager directly
    print(f"\nHierarchy manager - living_thing actions: {hierarchy_manager.get_inherited_actions('living_thing')}")
    print(f"Hierarchy manager - animal actions: {hierarchy_manager.get_inherited_actions('animal')}")

    # Test knowledge base action lookup
    actions_data = knowledge_base.get_all_actions_for_subject('cat')
    print(f"\nKnowledge base - cat actions: {actions_data}")

if __name__ == "__main__":
    test_logical_reasoning()
