<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English LLM Chatbot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 20px 20px 0 0;
        }
        
        .chat-header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            font-weight: 300;
        }
        
        .chat-header p {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8em;
            color: #666;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.active {
            background: #28a745;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            flex-shrink: 0;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .message.bot .message-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }
        
        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
        }
        
        .message-time {
            font-size: 0.7em;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            max-height: 120px;
            min-height: 50px;
            transition: border-color 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .send-button {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            font-size: 1.2em;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .send-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            border-bottom-left-radius: 5px;
            max-width: 70%;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4facfe;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
        
        .welcome-message {
            text-align: center;
            color: #666;
            padding: 40px 20px;
            font-style: italic;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 15px;
            padding: 15px;
            margin: 10px 0;
        }
        
        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 15px;
            }
            
            .message-content {
                max-width: 85%;
            }
            
            .status-bar {
                padding: 8px 15px;
            }
            
            .status-item {
                font-size: 0.7em;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 English LLM Assistant</h1>
            <p>Ask me questions or share text for analysis</p>
        </div>
        
        <div class="status-bar" id="statusBar">
            <div class="status-item">
                <span class="status-indicator" id="enhancedParserStatus"></span>
                <span>Parser</span>
            </div>
            <div class="status-item">
                <span class="status-indicator" id="dependencyParserStatus"></span>
                <span>Grammar</span>
            </div>
            <div class="status-item">
                <span class="status-indicator" id="contextMemoryStatus"></span>
                <span>Memory</span>
            </div>
            <div class="status-item">
                <span class="status-indicator" id="reasoningEngineStatus"></span>
                <span>Reasoning</span>
            </div>
            <div class="status-item">
                <span class="status-indicator" id="conditionalEngineStatus"></span>
                <span>Logic</span>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                👋 Welcome! I can help you with:
                <br>• Answering questions ("Can cats run?", "What is...?")
                <br>• Analyzing sentence structure and grammar
                <br>• Understanding complex text and relationships
                <br><br>Just type your message below to get started!
            </div>
        </div>
        
        <div class="chat-input">
            <div class="input-container">
                <textarea 
                    id="messageInput" 
                    class="input-field" 
                    placeholder="Type your message here..."
                    rows="1"></textarea>
                <button id="sendButton" class="send-button" onclick="sendMessage()">
                    ➤
                </button>
            </div>
        </div>
    </div>
    
    <script>
        let isWaitingForResponse = false;
        
        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStatus();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            const messageInput = document.getElementById('messageInput');
            
            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // Send message on Enter (but allow Shift+Enter for new lines)
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }
        
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                updateStatusIndicators(status);
            } catch (error) {
                console.error('Failed to load system status:', error);
            }
        }
        
        function updateStatusIndicators(status) {
            const statusMap = {
                'enhancedParserStatus': 'enhanced_parser',
                'dependencyParserStatus': 'dependency_parser',
                'contextMemoryStatus': 'context_memory',
                'reasoningEngineStatus': 'reasoning_engine',
                'conditionalEngineStatus': 'conditional_engine'
            };
            
            Object.entries(statusMap).forEach(([elementId, statusKey]) => {
                const element = document.getElementById(elementId);
                if (element && status[statusKey]) {
                    element.classList.add('active');
                }
            });
        }
        
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || isWaitingForResponse) {
                return;
            }
            
            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input and reset height
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Disable send button
            isWaitingForResponse = true;
            document.getElementById('sendButton').disabled = true;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: message })
                });
                
                const data = await response.json();
                
                // Hide typing indicator
                hideTypingIndicator();
                
                if (response.ok) {
                    addMessage(data.response, 'bot');
                } else {
                    addMessage(data.response || 'Sorry, I encountered an error.', 'bot', true);
                }
            } catch (error) {
                hideTypingIndicator();
                addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot', true);
            } finally {
                // Re-enable send button
                isWaitingForResponse = false;
                document.getElementById('sendButton').disabled = false;
                messageInput.focus();
            }
        }
        
        function addMessage(content, sender, isError = false) {
            const chatMessages = document.getElementById('chatMessages');
            
            // Remove welcome message if it exists
            const welcomeMessage = chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = sender === 'user' ? '👤' : '🤖';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            
            if (isError) {
                messageContent.innerHTML = `<div class="error-message">${content}</div>`;
            } else {
                messageContent.textContent = content;
            }
            
            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString();
            messageContent.appendChild(messageTime);
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            chatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function showTypingIndicator() {
            const chatMessages = document.getElementById('chatMessages');
            
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message bot';
            typingDiv.id = 'typingIndicator';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';
            
            const typingContent = document.createElement('div');
            typingContent.className = 'typing-indicator';
            typingContent.style.display = 'block';
            typingContent.innerHTML = `
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;
            
            typingDiv.appendChild(avatar);
            typingDiv.appendChild(typingContent);
            
            chatMessages.appendChild(typingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
    </script>
</body>
</html>