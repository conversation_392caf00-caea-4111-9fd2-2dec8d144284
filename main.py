#!/usr/bin/env python3
"""
Main entry point for the Hierarchy-Aware Knowledge Engine.

This script provides a simple command-line interface to interact with the knowledge engine.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.knowledge_engine_v3 import HierarchyAwareKnowledgeEngine
from core.logger import logger

def main():
    """Main function to run the knowledge engine."""
    try:
        # Initialize the knowledge engine
        engine = HierarchyAwareKnowledgeEngine()
        
        print("🧠 Hierarchy-Aware Knowledge Engine")
        print("Type 'quit' or 'exit' to stop.\n")
        
        while True:
            try:
                user_input = input("Enter a sentence or question: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye! 👋")
                    break
                
                if not user_input:
                    continue
                
                # Check if it's a question
                if user_input.endswith('?'):
                    result = engine.answer_question(user_input)
                    if result and result.get('success'):
                        print(f"Answer: {result.get('answer', 'No answer found')}")
                    else:
                        print("❌ Could not answer the question.")
                else:
                    # Process as a sentence
                    result = engine.process_sentence(user_input)
                    if result and result.get('success'):
                        print("✅ Sentence processed successfully!")
                        if result.get('learned_new_fact'):
                            print("📚 Learned something new!")
                    else:
                        print("❌ Could not process the sentence.")
                        
                print()  # Empty line for readability
                
            except KeyboardInterrupt:
                print("\nGoodbye! 👋")
                break
            except Exception as e:
                logger.error(f"Error processing input: {e}")
                print(f"❌ Error: {e}")
                
    except Exception as e:
        logger.error(f"Failed to initialize knowledge engine: {e}")
        print(f"❌ Failed to start: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()