#!/usr/bin/env python3
"""
Parsers for converting natural language into structured data.
"""

import spacy
from typing import Dict, Any, Optional

# Load the SpaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    print("SpaCy model 'en_core_web_sm' not found.")
    print("Please run: python -m spacy download en_core_web_sm")
    nlp = None

class SentenceParser:
    """Parses declarative sentences to extract subject, verb, and object."""

    def parse(self, sentence: str) -> Optional[Dict[str, Any]]:
        """
        Parses a sentence into its core components.
        
        Returns:
            A dictionary with 'subject', 'verb', 'object', or raises ValueError if parsing fails.
        """
        if not nlp:
            raise ImportError("SpaCy model is not loaded. Cannot parse sentence.")
            
        doc = nlp(sentence)
        
        subject = None
        verb = None
        obj = None

        # Find the root verb and its subject/object
        for token in doc:
            if token.pos_ == "VERB" and token.dep_ == "ROOT":
                verb = token.lemma_
                for child in token.children:
                    if child.dep_ == "nsubj":
                        subject = child.text
                    elif child.dep_ == "dobj":
                        obj = child.text
                break
        
        # Fallback for simple "is a" sentences (e.g., "A cat is an animal")
        if not verb:
            for token in doc:
                if token.lemma_ == "be" and token.dep_ == "ROOT":
                    verb = "is_a" # Special verb for "is a" relationships
                    for child in token.children:
                        if child.dep_ == "nsubj":
                            subject = child.text
                        elif child.dep_ == "attr":
                            obj = child.text
                    break

        if subject and verb:
            return {
                "subject": subject.lower(),
                "verb": verb.lower(),
                "object": obj.lower() if obj else None
            }
        
        raise ValueError(f"Could not parse sentence: '{sentence}' into subject, verb, object.")

class QuestionParser:
    """Parses questions to understand their intent and subject."""

    def parse_question(self, question: str) -> Optional[Dict[str, Any]]:
        """
        Parses a question to determine its type and subject.
        """
        question_lower = question.lower().strip()
        if not question_lower.endswith('?'):
            question_lower += '?'

        # Simple pattern matching for different question types
        if question_lower.startswith("what can"):
            parts = question.split()
            if len(parts) >= 4:
                return {"type": "what_can_do", "subject": parts[2], "context": {}}
        
        elif question_lower.startswith("what is"):
            subject = question[len("what is"):].strip().replace('?', '')
            return {"type": "what_is", "subject": subject, "context": {}}

        elif question_lower.startswith("who is"):
            subject = question[len("who is"):].strip().replace('?', '')
            return {"type": "who_is", "subject": subject, "context": {}}

        elif question_lower.startswith("where is"):
            subject = question[len("where is"):].strip().replace('?', '')
            return {"type": "where_is", "subject": subject, "context": {}}

        elif question_lower.startswith("how many"):
            parts = question.split()
            if len(parts) >= 3:
                return {"type": "how_many", "subject": parts[2].replace('?', ''), "context": {}}
        
        elif question_lower.startswith("what type is"):
            subject = question[len("what type is"):].strip().replace('?', '')
            return {"type": "what_type", "subject": subject, "context": {}}

        return None # Return None if no pattern matches

"""
Sentence parser for the Human-Like Language Engine
"""

import re
from typing import Dict, Any, Optional, List, Tuple
from config import config
from logger import logger, ParseError

class SentenceParser:
    """Handles sentence parsing with multiple grammar patterns"""
    
    def __init__(self):
        self.grammar_rules = config.get('grammar.sentence_patterns', {
            'svo': ['subject', 'verb', 'object'],
            'sv': ['subject', 'verb'],
            'svo_is': ['subject', 'verb', 'object']  # For "X is Y" patterns
        })
        self.supported_patterns = config.get('grammar.supported_patterns', ['svo', 'sv', 'svo_is'])
        self.max_sentence_length = config.get('system.max_sentence_length', 10)
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse sentence into structured format"""
        try:
            # Preprocessing
            cleaned_text = self._preprocess_text(text)
            words = self._tokenize(cleaned_text)
            
            # Validate sentence length
            if len(words) > self.max_sentence_length:
                raise ParseError(f"Sentence too long: {len(words)} words (max: {self.max_sentence_length})")
            
            if len(words) == 0:
                raise ParseError("Empty sentence")
            
            # Try to match grammar patterns
            parsed_result = self._match_grammar_pattern(words)
            
            if parsed_result:
                logger.debug(f"Parsed sentence successfully", 
                           pattern=parsed_result['pattern'], 
                           words=len(words))
                return parsed_result
            else:
                raise ParseError(f"No matching grammar pattern for {len(words)} words")
                
        except Exception as e:
            logger.error(f"Failed to parse sentence: '{text}'", exception=e)
            if isinstance(e, ParseError):
                raise
            else:
                raise ParseError(f"Parsing error: {str(e)}")
    
    def _preprocess_text(self, text: str) -> str:
        """Clean and normalize input text"""
        if not isinstance(text, str):
            raise ParseError(f"Input must be string, got {type(text)}")
        
        # Convert to lowercase and strip whitespace
        cleaned = text.lower().strip()
        
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # Remove punctuation (basic for now)
        cleaned = re.sub(r'[.,!?;:]', '', cleaned)
        
        return cleaned
    
    def _tokenize(self, text: str) -> List[str]:
        """Split text into tokens/words"""
        if not text:
            return []
        
        # Simple whitespace tokenization for now
        tokens = text.split()
        
        # Filter out empty tokens
        tokens = [token for token in tokens if token.strip()]
        
        return tokens
    
    def _match_grammar_pattern(self, words: List[str]) -> Optional[Dict[str, Any]]:
        """Try to match words against known grammar patterns"""
        word_count = len(words)
        
        # Special handling for "is" patterns
        if word_count == 3 and words[1] == 'is':
            return self._create_parse_result('svo_is', ['subject', 'verb', 'object'], words)
        
        # Try each supported pattern
        for pattern_name in self.supported_patterns:
            pattern = self.grammar_rules.get(pattern_name)
            if not pattern:
                continue
            
            if len(pattern) == word_count:
                result = self._create_parse_result(pattern_name, pattern, words)
                if result:
                    return result
        
        return None
    
    def _create_parse_result(self, pattern_name: str, pattern: List[str], words: List[str]) -> Dict[str, Any]:
        """Create structured parse result"""
        try:
            result = {
                'pattern': pattern_name,
                'raw_text': ' '.join(words),
                'word_count': len(words)
            }
            
            # Map words to grammatical roles
            for i, role in enumerate(pattern):
                if i < len(words):
                    result[role] = words[i]
                else:
                    result[role] = None
            
            # Ensure object is None for patterns without object
            if 'object' not in result:
                result['object'] = None
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to create parse result", exception=e)
            return None
    
    def validate_parse_structure(self, parsed: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate the structure of a parsed sentence"""
        try:
            # Check required fields
            required_fields = ['pattern', 'subject', 'verb']
            for field in required_fields:
                if field not in parsed or parsed[field] is None:
                    return False, f"Missing required field: {field}"
            
            # Check pattern validity
            if parsed['pattern'] not in self.supported_patterns:
                return False, f"Unsupported pattern: {parsed['pattern']}"
            
            # Check word constraints
            subject = parsed.get('subject', '')
            verb = parsed.get('verb', '')
            obj = parsed.get('object')
            
            if not subject or not isinstance(subject, str):
                return False, "Invalid subject"
            
            if not verb or not isinstance(verb, str):
                return False, "Invalid verb"
            
            # For SVO pattern, object should exist
            if parsed['pattern'] in ['svo', 'svo_is'] and not obj:
                return False, "SVO pattern requires object"
            
            # For SV pattern, object should be None
            if parsed['pattern'] == 'sv' and obj is not None:
                return False, "SV pattern should not have object"
            
            return True, "Valid structure"
            
        except Exception as e:
            logger.error(f"Error validating parse structure", exception=e)
            return False, f"Validation error: {str(e)}"
    
    def get_supported_patterns(self) -> List[str]:
        """Get list of supported grammar patterns"""
        return self.supported_patterns.copy()
    
    def add_grammar_pattern(self, name: str, pattern: List[str]) -> None:
        """Add new grammar pattern (for future extensibility)"""
        if name in self.grammar_rules:
            logger.warning(f"Grammar pattern already exists: {name}")
            return
        
        self.grammar_rules[name] = pattern
        if name not in self.supported_patterns:
            self.supported_patterns.append(name)
        
        logger.info(f"Added grammar pattern: {name}", pattern=pattern)
    
    def get_parse_statistics(self) -> Dict[str, Any]:
        """Get parser statistics"""
        return {
            'supported_patterns': len(self.supported_patterns),
            'grammar_rules': list(self.grammar_rules.keys()),
            'max_sentence_length': self.max_sentence_length
        }

class QuestionParser:
    """Specialized parser for questions"""
    
    def __init__(self):
        self.question_patterns = {
            'what_can_do': r'^what can (\w+) do\?$',
            'what_is': r'^what is (\w+)\?$',
            'who_is': r'^who is (\w+)\?$',
            'where_is': r'^where is (\w+)\?$',
            'what_type_is': r'^what type is (\w+)\?$',
            'how_many': r'^how many (\w+)\?$'
        }
    
    def parse_question(self, question: str) -> Optional[Dict[str, Any]]:
        """Parse question into structured format"""
        try:
            cleaned_question = question.lower().strip()
            
            for pattern_name, pattern in self.question_patterns.items():
                match = re.match(pattern, cleaned_question)
                if match:
                    return {
                        'type': pattern_name,
                        'subject': match.group(1),
                        'raw_question': question
                    }
            
            logger.debug(f"No pattern matched for question: {question}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse question: '{question}'", exception=e)
            return None
    
    def is_question(self, text: str) -> bool:
        """Check if text is a question"""
        return text.strip().endswith('?')