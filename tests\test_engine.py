#!/usr/bin/env python3
"""
Interactive Test Runner for Knowledge Engine
Allows manual testing and experimentation
"""

from knowledge_engine import KnowledgeEngine

def interactive_mode():
    """Interactive testing mode"""
    engine = KnowledgeEngine()
    
    print("🧠 Interactive Language Engine Test")
    print("====================================")
    print("Commands:")
    print("  - Type a sentence to validate it")
    print("  - Type a question (what can X do? / what is X?)")
    print("  - 'learn X action Y' to teach new action")
    print("  - 'learn X type Y' to teach new type")
    print("  - 'show X' to see what we know about X")
    print("  - 'quit' to exit\n")
    
    while True:
        try:
            user_input = input("🤖 > ").strip()
            
            if user_input.lower() == 'quit':
                print("Goodbye! 👋")
                break
            
            # Learning commands
            if user_input.startswith('learn '):
                parts = user_input.split()
                if len(parts) == 4 and parts[2] in ['action', 'type', 'property']:
                    subject, fact_type, fact_value = parts[1], parts[2], parts[3]
                    result = engine.learn_new_fact(subject, fact_type, fact_value)
                    print(f"📚 {result}")
                else:
                    print("❌ Format: learn <subject> <action/type/property> <value>")
                continue
            
            # Show knowledge command
            if user_input.startswith('show '):
                subject = user_input[5:]
                if subject in engine.knowledge:
                    info = engine.knowledge[subject]
                    print(f"📖 {subject}:")
                    print(f"   Type: {info['type']}")
                    print(f"   Actions: {info.get('actions', [])}")
                    print(f"   Properties: {info.get('properties', [])}")
                else:
                    print(f"❌ Don't know about '{subject}'")
                continue
            
            # Questions
            if '?' in user_input:
                answer = engine.answer_question(user_input)
                print(f"💭 {answer}")
                continue
            
            # Sentence validation
            parsed = engine.parse_sentence(user_input)
            if parsed:
                valid, reason = engine.validate_sentence(parsed)
                status = "✅" if valid else "❌"
                print(f"{status} {reason}")
                
                if valid:
                    # Show the parsed structure
                    print(f"📝 Parsed: {parsed}")
            else:
                print("❌ Cannot parse sentence (try: 'subject verb' or 'subject verb object')")
                
        except KeyboardInterrupt:
            print("\nGoodbye! 👋")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def run_automated_tests():
    """Run comprehensive automated tests"""
    engine = KnowledgeEngine()
    
    print("🧪 Running Automated Tests")
    print("==========================\n")
    
    # Test 1: Valid sentences
    print("Test 1: Valid Sentences")
    valid_sentences = [
        "cat chase mouse",
        "dog bark",
        "cat sleep",
        "mouse eat food",
        "dog eat food"
    ]
    
    for sentence in valid_sentences:
        parsed = engine.parse_sentence(sentence)
        valid, reason = engine.validate_sentence(parsed)
        assert valid, f"Expected '{sentence}' to be valid, but got: {reason}"
        print(f"✅ '{sentence}' - {reason}")
    
    # Test 2: Invalid sentences
    print("\nTest 2: Invalid Sentences")
    invalid_sentences = [
        "table chase cat",  # tables can't chase
        "cat bark",        # cats don't bark
        "food sleep",      # food can't sleep
        "dog meow"         # dogs don't meow
    ]
    
    for sentence in invalid_sentences:
        parsed = engine.parse_sentence(sentence)
        valid, reason = engine.validate_sentence(parsed)
        assert not valid, f"Expected '{sentence}' to be invalid, but it was accepted"
        print(f"❌ '{sentence}' - {reason}")
    
    # Test 3: Questions
    print("\nTest 3: Question Answering")
    questions = [
        ("what can cat do?", "cat can: chase, sleep, eat, meow"),
        ("what is dog?", "dog is a animal"),
        ("what can unknown do?", "I don't know about unknown")
    ]
    
    for question, expected in questions:
        answer = engine.answer_question(question)
        print(f"Q: {question}")
        print(f"A: {answer}")
        # Note: We're not asserting exact matches since answers might vary
        print()
    
    # Test 4: Learning
    print("Test 4: Learning New Facts")
    result = engine.learn_new_fact("cat", "action", "jump")
    print(f"📚 {result}")
    
    # Verify learning worked
    answer = engine.answer_question("what can cat do?")
    assert "jump" in answer, "Learning failed - 'jump' not found in cat's actions"
    print(f"✅ Verified: {answer}")
    
    print("\n🎉 All tests passed!")

def main():
    """Main entry point"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        run_automated_tests()
    else:
        interactive_mode()

if __name__ == "__main__":
    main()