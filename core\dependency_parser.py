#!/usr/bin/env python3
"""
Dependency Parser using SpaCy
Provides deep grammatical structure analysis for enhanced understanding
"""

import re
from typing import Dict, Any, Optional, List, Tuple, Set
from dataclasses import dataclass
from enum import Enum
try:
    from logger import logger, ParseError
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    
    class ParseError(Exception):
        """Custom exception for parsing errors"""
        pass

try:
    import spacy
    from spacy.tokens import Doc, Token
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logger.warning("SpaCy not available. Dependency parser will use fallback rules.")

class DependencyRelation(Enum):
    """Common dependency relations"""
    SUBJECT = "nsubj"  # Nominal subject
    OBJECT = "dobj"   # Direct object
    INDIRECT_OBJECT = "iobj"  # Indirect object
    MODIFIER = "amod"  # Adjectival modifier
    DETERMINER = "det"  # Determiner
    PREPOSITION = "prep"  # Prepositional modifier
    CONJUNCTION = "conj"  # Conjunct
    AUXILIARY = "aux"  # Auxiliary
    ROOT = "ROOT"  # Root of the sentence
    COMPOUND = "compound"  # Compound
    NEGATION = "neg"  # Negation modifier
    ADVERBIAL = "advmod"  # Adverbial modifier

@dataclass
class DependencyToken:
    """Represents a token with its dependency information"""
    text: str
    lemma: str
    pos: str  # Part of speech
    tag: str  # Detailed POS tag
    dep: str  # Dependency relation
    head_idx: int  # Index of head token
    children_idx: List[int]  # Indices of dependent tokens
    is_root: bool = False
    
@dataclass
class DependencyParse:
    """Complete dependency parse of a sentence"""
    tokens: List[DependencyToken]
    root_idx: int
    sentence: str
    entities: List[Dict[str, Any]]  # Named entities
    noun_phrases: List[str]  # Noun phrases
    verb_phrases: List[str]  # Verb phrases
    dependencies: Dict[str, List[int]]  # Grouped by relation type
    
class DependencyParser:
    """Advanced dependency parser using SpaCy with fallback rules"""
    
    def __init__(self, model_name: str = "en_core_web_sm"):
        self.model_name = model_name
        self.nlp = None
        self.use_spacy = False
        
        # Initialize SpaCy if available
        if SPACY_AVAILABLE:
            try:
                self.nlp = spacy.load(model_name)
                self.use_spacy = True
                logger.info(f"SpaCy model '{model_name}' loaded successfully")
            except OSError:
                logger.warning(f"SpaCy model '{model_name}' not found. Using fallback rules.")
                self.use_spacy = False
        
        # Fallback rules for when SpaCy is not available
        self.pos_patterns = {
            'NOUN': r'\b(cat|dog|bird|mammal|animal|person|house|car)s?\b',
            'VERB': r'\b(is|are|can|cannot|has|have|runs?|flies?|walks?)\b',
            'ADJ': r'\b(big|small|red|blue|fast|slow|good|bad)\b',
            'DET': r'\b(the|a|an|this|that|these|those)\b',
            'PREP': r'\b(in|on|at|by|for|with|from|to|of)\b'
        }
        
        # Common dependency patterns for fallback
        self.dependency_rules = [
            (r'^(\w+)\s+(is|are)\s+(\w+)$', ['nsubj', 'ROOT', 'attr']),
            (r'^(\w+)\s+(can|cannot)\s+(\w+)$', ['nsubj', 'aux', 'ROOT']),
            (r'^(\w+)\s+(\w+)\s+(\w+)$', ['nsubj', 'ROOT', 'dobj'])
        ]
    
    def parse(self, text: str) -> DependencyParse:
        """Parse text and return dependency structure"""
        if not text or not text.strip():
            raise ParseError("Empty text provided")
        
        text = text.strip()
        
        if self.use_spacy:
            return self._parse_with_spacy(text)
        else:
            return self._parse_with_fallback(text)
    
    def _parse_with_spacy(self, text: str) -> DependencyParse:
        """Parse using SpaCy's dependency parser"""
        doc = self.nlp(text)
        
        tokens = []
        root_idx = -1
        dependencies = {}
        
        for i, token in enumerate(doc):
            # Find children indices
            children_idx = [child.i for child in token.children]
            
            # Create dependency token
            dep_token = DependencyToken(
                text=token.text,
                lemma=token.lemma_,
                pos=token.pos_,
                tag=token.tag_,
                dep=token.dep_,
                head_idx=token.head.i if token.head != token else i,
                children_idx=children_idx,
                is_root=(token.dep_ == "ROOT")
            )
            
            tokens.append(dep_token)
            
            # Track root
            if token.dep_ == "ROOT":
                root_idx = i
            
            # Group by dependency relation
            if token.dep_ not in dependencies:
                dependencies[token.dep_] = []
            dependencies[token.dep_].append(i)
        
        # Extract entities
        entities = [
            {
                'text': ent.text,
                'label': ent.label_,
                'start': ent.start,
                'end': ent.end
            }
            for ent in doc.ents
        ]
        
        # Extract noun and verb phrases
        noun_phrases = [chunk.text for chunk in doc.noun_chunks]
        verb_phrases = self._extract_verb_phrases(doc)
        
        return DependencyParse(
            tokens=tokens,
            root_idx=root_idx,
            sentence=text,
            entities=entities,
            noun_phrases=noun_phrases,
            verb_phrases=verb_phrases,
            dependencies=dependencies
        )
    
    def _parse_with_fallback(self, text: str) -> DependencyParse:
        """Parse using fallback rules when SpaCy is not available"""
        words = text.lower().split()
        tokens = []
        root_idx = -1
        dependencies = {}
        
        # Try to match against known patterns
        matched_pattern = None
        for pattern, deps in self.dependency_rules:
            if re.match(pattern, text.lower()):
                matched_pattern = deps
                break
        
        # Create tokens with basic POS and dependency info
        for i, word in enumerate(words):
            pos = self._guess_pos(word)
            dep = matched_pattern[i] if matched_pattern and i < len(matched_pattern) else "unk"
            
            # Simple head assignment
            if dep == "ROOT":
                head_idx = i
                root_idx = i
            elif dep == "nsubj":
                head_idx = self._find_verb_index(words)
            elif dep in ["dobj", "attr"]:
                head_idx = self._find_verb_index(words)
            else:
                head_idx = max(0, i - 1)  # Default to previous word
            
            token = DependencyToken(
                text=word,
                lemma=word,  # Simple lemmatization
                pos=pos,
                tag=pos,
                dep=dep,
                head_idx=head_idx,
                children_idx=[],
                is_root=(dep == "ROOT")
            )
            
            tokens.append(token)
            
            # Group by dependency
            if dep not in dependencies:
                dependencies[dep] = []
            dependencies[dep].append(i)
        
        # Update children indices
        for i, token in enumerate(tokens):
            for j, other_token in enumerate(tokens):
                if other_token.head_idx == i and i != j:
                    token.children_idx.append(j)
        
        return DependencyParse(
            tokens=tokens,
            root_idx=root_idx if root_idx >= 0 else 0,
            sentence=text,
            entities=[],  # No entity recognition in fallback
            noun_phrases=self._extract_simple_noun_phrases(words),
            verb_phrases=self._extract_simple_verb_phrases(words),
            dependencies=dependencies
        )
    
    def _guess_pos(self, word: str) -> str:
        """Guess part of speech using simple patterns"""
        word_lower = word.lower()
        
        for pos, pattern in self.pos_patterns.items():
            if re.search(pattern, word_lower):
                return pos
        
        # Default heuristics
        if word_lower.endswith('ly'):
            return 'ADV'
        elif word_lower.endswith('ing') or word_lower.endswith('ed'):
            return 'VERB'
        elif word_lower.endswith('s') and len(word_lower) > 2:
            return 'NOUN'
        else:
            return 'NOUN'  # Default to noun
    
    def _find_verb_index(self, words: List[str]) -> int:
        """Find the main verb in a list of words"""
        for i, word in enumerate(words):
            if self._guess_pos(word) == 'VERB':
                return i
        return 0  # Default to first word
    
    def _extract_verb_phrases(self, doc) -> List[str]:
        """Extract verb phrases from SpaCy doc"""
        verb_phrases = []
        
        for token in doc:
            if token.pos_ == "VERB":
                # Get verb and its auxiliaries/modifiers
                phrase_tokens = [token]
                
                for child in token.children:
                    if child.dep_ in ["aux", "auxpass", "neg", "advmod"]:
                        phrase_tokens.append(child)
                
                # Sort by position and create phrase
                phrase_tokens.sort(key=lambda t: t.i)
                phrase = " ".join([t.text for t in phrase_tokens])
                verb_phrases.append(phrase)
        
        return verb_phrases
    
    def _extract_simple_noun_phrases(self, words: List[str]) -> List[str]:
        """Extract simple noun phrases using basic patterns"""
        noun_phrases = []
        
        for i, word in enumerate(words):
            if self._guess_pos(word) == 'NOUN':
                phrase = []
                
                # Look for determiners and adjectives before
                j = i - 1
                while j >= 0 and self._guess_pos(words[j]) in ['DET', 'ADJ']:
                    phrase.insert(0, words[j])
                    j -= 1
                
                phrase.append(word)
                
                # Look for compound nouns after
                j = i + 1
                while j < len(words) and self._guess_pos(words[j]) == 'NOUN':
                    phrase.append(words[j])
                    j += 1
                
                if len(phrase) > 1 or word not in [p.split()[-1] for p in noun_phrases]:
                    noun_phrases.append(" ".join(phrase))
        
        return noun_phrases
    
    def _extract_simple_verb_phrases(self, words: List[str]) -> List[str]:
        """Extract simple verb phrases using basic patterns"""
        verb_phrases = []
        
        for i, word in enumerate(words):
            if self._guess_pos(word) == 'VERB':
                phrase = [word]
                
                # Look for auxiliaries before
                if i > 0 and words[i-1].lower() in ['can', 'cannot', 'will', 'would', 'should']:
                    phrase.insert(0, words[i-1])
                
                # Look for negation
                if i < len(words) - 1 and words[i+1].lower() in ['not', "n't"]:
                    phrase.append(words[i+1])
                
                verb_phrases.append(" ".join(phrase))
        
        return verb_phrases
    
    def get_subjects(self, parse: DependencyParse) -> List[str]:
        """Extract all subjects from the parse"""
        subjects = []
        
        for relation, indices in parse.dependencies.items():
            if 'subj' in relation.lower():
                for idx in indices:
                    if idx < len(parse.tokens):
                        subjects.append(parse.tokens[idx].text)
        
        return subjects
    
    def get_objects(self, parse: DependencyParse) -> List[str]:
        """Extract all objects from the parse"""
        objects = []
        
        for relation, indices in parse.dependencies.items():
            if 'obj' in relation.lower() or relation == 'attr':
                for idx in indices:
                    if idx < len(parse.tokens):
                        objects.append(parse.tokens[idx].text)
        
        return objects
    
    def get_root_verb(self, parse: DependencyParse) -> Optional[str]:
        """Get the main verb (root) of the sentence"""
        if parse.root_idx >= 0 and parse.root_idx < len(parse.tokens):
            return parse.tokens[parse.root_idx].text
        return None
    
    def analyze_sentence_structure(self, parse: DependencyParse) -> Dict[str, Any]:
        """Analyze the overall structure of the sentence"""
        structure = {
            'subjects': self.get_subjects(parse),
            'objects': self.get_objects(parse),
            'root_verb': self.get_root_verb(parse),
            'noun_phrases': parse.noun_phrases,
            'verb_phrases': parse.verb_phrases,
            'entities': parse.entities,
            'dependency_types': list(parse.dependencies.keys()),
            'token_count': len(parse.tokens),
            'complexity_score': self._calculate_complexity(parse)
        }
        
        return structure
    
    def _calculate_complexity(self, parse: DependencyParse) -> float:
        """Calculate a complexity score for the sentence"""
        # Base score
        score = len(parse.tokens) * 0.1
        
        # Add for different dependency types
        score += len(parse.dependencies) * 0.2
        
        # Add for entities
        score += len(parse.entities) * 0.3
        
        # Add for complex phrases
        score += len([np for np in parse.noun_phrases if len(np.split()) > 2]) * 0.4
        score += len([vp for vp in parse.verb_phrases if len(vp.split()) > 1]) * 0.3
        
        return round(score, 2)
    
    def is_available(self) -> bool:
        """Check if SpaCy is available and loaded"""
        return self.use_spacy
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        if self.use_spacy and self.nlp:
            return {
                'model_name': self.model_name,
                'spacy_version': spacy.__version__,
                'pipeline': self.nlp.pipe_names,
                'available': True
            }
        else:
            return {
                'model_name': 'fallback_rules',
                'spacy_version': None,
                'pipeline': ['basic_pos', 'basic_deps'],
                'available': False
            }