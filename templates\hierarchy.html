<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Hierarchy Visualization</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: auto;
        }

        .container {
            max-width: 100vw;
            margin: 0;
            background: rgba(255, 255, 255, 0.95);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .toolbar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            position: sticky;
            top: 80px;
            z-index: 99;
        }

        .view-toggle {
            display: flex;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .view-btn {
            padding: 10px 20px;
            border: none;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .view-btn.active {
            background: #4facfe;
            color: white;
        }

        .control-btn {
            padding: 8px 16px;
            border: none;
            background: #4facfe;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .control-btn:hover {
            background: #3a8bfe;
            transform: translateY(-1px);
        }

        .search-box {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            width: 200px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #4facfe;
        }

        .stats-bar {
            background: white;
            padding: 10px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 30px;
            font-size: 14px;
            color: #6c757d;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-number {
            font-weight: bold;
            color: #4facfe;
        }

        /* Flowchart View Styles */
        .flowchart-container {
            padding: 30px;
            overflow: auto;
            min-height: 70vh;
            position: relative;
        }

        .flowchart-node {
            position: absolute;
            background: white;
            border: 2px solid #4facfe;
            border-radius: 12px;
            padding: 15px 20px;
            min-width: 150px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            user-select: none;
        }

        .flowchart-node:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #00f2fe;
        }

        .flowchart-node.selected {
            border-color: #ff6b6b;
            background: #fff5f5;
        }

        .node-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .node-level {
            font-size: 12px;
            color: #6c757d;
            background: #f8f9fa;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
        }

        .node-emoji {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .connection-line {
            position: absolute;
            overflow: visible;
            pointer-events: none;
            z-index: 1;
        }

        .connection-line path {
            stroke: #a0c4ff;
            stroke-width: 2px;
            fill: none;
            transition: stroke 0.3s ease;
        }

        .flowchart-node.selected ~ svg.connection-line path,
        .flowchart-node:hover ~ svg.connection-line path {
            stroke: #4facfe;
            stroke-width: 3px;
        }

        /* Folder Tree View Styles */
        .folder-container {
            padding: 20px;
            background: #f8f9fa;
            min-height: 70vh;
        }

        .folder-tree {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .folder-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .folder-item:hover {
            background: #f0f8ff;
            transform: translateX(5px);
        }

        .folder-item.selected {
            background: #e3f2fd;
            border-left: 4px solid #4facfe;
        }

        .folder-icon {
            margin-right: 10px;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        .folder-content {
            flex: 1;
        }

        .folder-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .folder-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        .folder-children {
            margin-left: 30px;
            border-left: 2px solid #e9ecef;
            padding-left: 15px;
            margin-top: 5px;
        }

        .folder-toggle {
            margin-right: 8px;
            font-size: 12px;
            color: #6c757d;
            cursor: pointer;
            width: 15px;
            text-align: center;
        }

        .folder-badge {
            background: #4facfe;
            color: white;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 8px;
        }

        /* Details Panel */
        .details-panel {
            position: fixed;
            right: -400px;
            top: 0;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .details-panel.open {
            right: 0;
        }

        .details-header {
            background: #4facfe;
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
        }

        .details-content {
            padding: 20px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5em;
            float: right;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .detail-section {
            margin-bottom: 20px;
        }

        .detail-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-list {
            list-style: none;
        }

        .detail-list li {
            background: #f8f9fa;
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 3px solid #4facfe;
        }

        .hidden {
            display: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .details-panel {
                width: 100%;
                right: -100%;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .view-toggle {
                width: 100%;
            }
            
            .search-box {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌳 Enhanced Hierarchy Visualization</h1>
            <p>Interactive flowchart and folder-tree view of your knowledge structure</p>
        </div>

        <div class="toolbar">
            <div class="view-toggle">
                <button class="view-btn active" data-view="flowchart">📊 Flowchart</button>
                <button class="view-btn" data-view="folder">📁 Folder Tree</button>
            </div>
            
            <input type="text" class="search-box" placeholder="🔍 Search nodes..." id="search-input">
            
            <button class="control-btn" id="expand-all">Expand All</button>
            <button class="control-btn" id="collapse-all">Collapse All</button>
            <button class="control-btn" id="center-view">Center View</button>
            <button class="control-btn" id="auto-layout">Auto Layout</button>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <span>📦 Total:</span>
                <span class="stat-number" id="total-nodes">0</span>
            </div>
            <div class="stat-item">
                <span>🌿 Roots:</span>
                <span class="stat-number" id="root-nodes">0</span>
            </div>
            <div class="stat-item">
                <span>🍃 Leaves:</span>
                <span class="stat-number" id="leaf-nodes">0</span>
            </div>
            <div class="stat-item">
                <span>📏 Max Depth:</span>
                <span class="stat-number" id="max-depth">0</span>
            </div>
        </div>

        <!-- Flowchart View -->
        <div id="flowchart-view" class="view-content">
            <div class="flowchart-container">
                <div class="flowchart-canvas"></div>
            </div>
        </div>

        <!-- Folder Tree View -->
        <div class="folder-container hidden" id="folder-view">
            <div class="folder-tree" id="folder-tree">
                <!-- Folder tree will be rendered here -->
            </div>
        </div>
    </div>

    <!-- Details Panel -->
    <div class="details-panel" id="details-panel">
        <div class="details-header">
            <button class="close-btn" id="close-details">&times;</button>
            <h3 id="details-title">Node Details</h3>
        </div>
        <div class="details-content" id="details-content">
            <!-- Details will be loaded here -->
        </div>
    </div>

    <script>
        class EnhancedHierarchyVisualizer {
    constructor() {
        this.hierarchyData = {};
        this.currentView = 'flowchart';
        this.expandedNodes = new Set(['entity']);
        this.selectedNode = null;
        this.searchTerm = '';
        this.nodePositions = {};
        this.rootNodes = [];

        this.scale = 1;
        this.panX = 0;
        this.panY = 0;
        this.isPanning = false;
        this.isDraggingNode = false;
        this.draggedNodeName = null;
        this.lastMouseX = 0;
        this.lastMouseY = 0;

        this.init();
    }

    async init() {
        this.flowchartContainer = document.getElementById('flowchart-view');
        this.flowchartCanvas = document.querySelector('.flowchart-canvas');
        this.folderContainer = document.getElementById('folder-view');
        this.folderTree = document.getElementById('folder-tree');
        this.detailsPanel = document.getElementById('details-panel');

        this.bindEvents();
        await this.loadHierarchyData();
        this.renderCurrentView();
        this.centerView();
    }

    async loadHierarchyData() {
        try {
            const response = await fetch('/api/hierarchy');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();

            if (data.success) {
                this.hierarchyData = data.hierarchy;
                this.rootNodes = Object.keys(this.hierarchyData).filter(name => !this.hierarchyData[name].parent);
                this.assignLevels();
                this.autoLayout();
                this.updateStatistics();
            } else {
                this.showError('Failed to load hierarchy data: ' + data.error);
            }
        } catch (error) {
            this.showError('Error loading hierarchy data: ' + error.message);
        }
    }

    assignLevels() {
        const processed = new Set();
        const setLevel = (nodeName, level) => {
            if (processed.has(nodeName)) return;
            const node = this.hierarchyData[nodeName];
            if (!node) return;
            node.level = level;
            processed.add(nodeName);
            if (node.children) {
                node.children.forEach(child => setLevel(child, level + 1));
            }
        };
        this.rootNodes.forEach(root => setLevel(root, 0));
        Object.keys(this.hierarchyData).forEach(name => {
            if (!this.hierarchyData[name].hasOwnProperty('level')) {
                 this.hierarchyData[name].level = 99; // Assign a high level for un-parented nodes
            }
        });
    }

    bindEvents() {
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', () => this.switchView(btn.dataset.view));
        });

        document.getElementById('search-input').addEventListener('input', e => {
            this.searchTerm = e.target.value.toLowerCase();
            this.renderCurrentView();
        });
        document.getElementById('expand-all').addEventListener('click', () => this.toggleAll(true));
        document.getElementById('collapse-all').addEventListener('click', () => this.toggleAll(false));
        document.getElementById('center-view').addEventListener('click', () => this.centerView());
        document.getElementById('auto-layout').addEventListener('click', () => {
            this.autoLayout();
            this.renderFlowchart();
        });

        document.getElementById('close-details').addEventListener('click', () => this.closeDetailsPanel());

        this.flowchartContainer.addEventListener('wheel', e => this.handleZoom(e), { passive: false });
        this.flowchartContainer.addEventListener('mousedown', e => this.startPanOrDrag(e));
        window.addEventListener('mousemove', e => this.handleDrag(e));
        window.addEventListener('mouseup', e => this.stopDrag(e));
    }

    switchView(view) {
        this.currentView = view;
        document.querySelectorAll('.view-btn').forEach(btn => btn.classList.toggle('active', btn.dataset.view === view));
        this.flowchartContainer.classList.toggle('hidden', view !== 'flowchart');
        this.folderContainer.classList.toggle('hidden', view !== 'folder');
        this.renderCurrentView();
    }

    renderCurrentView() {
        if (this.currentView === 'flowchart') {
            this.renderFlowchart();
        } else {
            this.renderFolderTree();
        }
    }

    updateStatistics() {
        const nodeCount = Object.keys(this.hierarchyData).length;
        const leafNodes = Object.values(this.hierarchyData).filter(n => !n.children || n.children.length === 0).length;
        const maxDepth = Math.max(-1, ...Object.values(this.hierarchyData).map(n => n.level || 0));
        document.getElementById('total-nodes').textContent = nodeCount;
        document.getElementById('root-nodes').textContent = this.rootNodes.length;
        document.getElementById('leaf-nodes').textContent = leafNodes;
        document.getElementById('max-depth').textContent = maxDepth;
    }

    renderFlowchart() {
        if (!this.flowchartCanvas) return;
        this.flowchartCanvas.innerHTML = '';
        const filteredNodes = this.getFilteredNodes();

        const nodesFragment = document.createDocumentFragment();
        filteredNodes.forEach(nodeName => {
            const node = this.hierarchyData[nodeName];
            const position = this.nodePositions[nodeName];
            if (position) {
                const nodeEl = this.createFlowchartNode(nodeName, node, position);
                nodesFragment.appendChild(nodeEl);
            }
        });

        this.flowchartCanvas.appendChild(nodesFragment);
        this.renderConnections(filteredNodes);
        this.updateCanvasTransform();
    }

    createFlowchartNode(nodeName, node, position) {
        const nodeEl = document.createElement('div');
        nodeEl.className = 'flowchart-node';
        nodeEl.dataset.node = nodeName;
        if (this.selectedNode === nodeName) nodeEl.classList.add('selected');
        
        nodeEl.style.left = `${position.x}px`;
        nodeEl.style.top = `${position.y}px`;
        
        nodeEl.innerHTML = `
            <div class="node-emoji">${this.getNodeEmoji(nodeName)}</div>
            <div class="node-title">${nodeName}</div>
            <div class="node-level">Level ${node.level}</div>
        `;
        
        nodeEl.addEventListener('click', e => {
            if (e.detail > 0) this.selectNode(nodeName);
        });
        return nodeEl;
    }

    renderConnections(filteredNodes) {
        const svgNS = "http://www.w3.org/2000/svg";
        const connectionsFragment = document.createDocumentFragment();
        filteredNodes.forEach(nodeName => {
            const node = this.hierarchyData[nodeName];
            if (node.children) {
                node.children.forEach(childName => {
                    if (filteredNodes.includes(childName)) {
                        const line = this.createConnectionLine(nodeName, childName);
                        if (line) connectionsFragment.appendChild(line);
                    }
                });
            }
        });
        this.flowchartCanvas.prepend(connectionsFragment);
    }

    createConnectionLine(parentName, childName) {
        const parentPos = this.nodePositions[parentName];
        const childPos = this.nodePositions[childName];
        if (!parentPos || !childPos) return null;

        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, 'svg');
        svg.classList.add('connection-line');
        const path = document.createElementNS(svgNS, 'path');

        const nodeWidth = 150;
        const nodeHeight = 95; 

        const x1 = parentPos.x + nodeWidth / 2;
        const y1 = parentPos.y + nodeHeight;
        const x2 = childPos.x + nodeWidth / 2;
        const y2 = childPos.y;

        const pathData = `M${x1},${y1} C${x1},${y1 + 60} ${x2},${y2 - 60} ${x2},${y2}`;
        path.setAttribute('d', pathData);
        svg.appendChild(path);

        const padding = 20;
        const minX = Math.min(x1, x2) - padding;
        const minY = Math.min(y1, y2) - padding;
        const maxX = Math.max(x1, x2) + padding;
        const maxY = Math.max(y1, y2) + padding;

        svg.style.left = `${minX}px`;
        svg.style.top = `${minY}px`;
        svg.setAttribute('width', `${maxX - minX}`);
        svg.setAttribute('height', `${maxY - minY}`);
        path.setAttribute('transform', `translate(${-minX}, ${-minY})`);

        return svg;
    }

    autoLayout() {
        const levels = {};
        const nodeWidth = 180;
        const nodeSpacing = 60;
        const levelHeight = 180;

        Object.entries(this.hierarchyData).forEach(([nodeName, node]) => {
            const level = node.level;
            if (!levels[level]) {
                levels[level] = [];
            }
            levels[level].push(nodeName);
        });

        const containerWidth = this.flowchartContainer.clientWidth;

        Object.entries(levels).forEach(([level, nodes]) => {
            const y = parseInt(level) * levelHeight + 50;
            const totalWidth = nodes.length * (nodeWidth + nodeSpacing) - nodeSpacing;
            let startX = (containerWidth - totalWidth) / 2;
            if (startX < 50) startX = 50;

            nodes.forEach((nodeName, index) => {
                this.nodePositions[nodeName] = {
                    x: startX + index * (nodeWidth + nodeSpacing),
                    y: y
                };
            });
        });
    }

    centerView() {
        console.log('Running centerView...');
        this.scale = 0.8;
        const container = this.flowchartContainer;
        const canvas = this.flowchartCanvas;
        const contentWidth = canvas.scrollWidth * this.scale;
        const contentHeight = canvas.scrollHeight * this.scale;
        this.panX = (container.clientWidth - contentWidth) / 2;
        this.panY = (container.clientHeight - contentHeight) / 4;
        this.updateCanvasTransform();
    }

    updateCanvasTransform() {
        if (this.flowchartCanvas) {
            this.flowchartCanvas.style.transform = `translate(${this.panX}px, ${this.panY}px) scale(${this.scale})`;
        }
    }

    handleZoom(e) {
        e.preventDefault();
        if (this.isDraggingNode) return;

        const rect = this.flowchartContainer.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = 1.1;
        const oldScale = this.scale;
        
        this.scale = e.deltaY < 0 ? oldScale * zoomFactor : oldScale / zoomFactor;
        this.scale = Math.max(0.2, Math.min(this.scale, 4));

        this.panX = mouseX - (mouseX - this.panX) * (this.scale / oldScale);
        this.panY = mouseY - (mouseY - this.panY) * (this.scale / oldScale);

        this.updateCanvasTransform();
    }

    startPanOrDrag(e) {
        const nodeEl = e.target.closest('.flowchart-node');
        if (nodeEl) {
            this.isDraggingNode = true;
            this.draggedNodeName = nodeEl.dataset.node;
            nodeEl.style.zIndex = 1000;
            nodeEl.style.cursor = 'grabbing';
        } else {
            this.isPanning = true;
            this.flowchartContainer.style.cursor = 'grabbing';
        }
        this.lastMouseX = e.clientX;
        this.lastMouseY = e.clientY;
    }

    handleDrag(e) {
        if (!this.isPanning && !this.isDraggingNode) return;
        e.preventDefault();

        const dx = e.clientX - this.lastMouseX;
        const dy = e.clientY - this.lastMouseY;

        if (this.isPanning) {
            this.panX += dx;
            this.panY += dy;
            this.updateCanvasTransform();
        } else if (this.isDraggingNode) {
            const pos = this.nodePositions[this.draggedNodeName];
            pos.x += dx / this.scale;
            pos.y += dy / this.scale;
            this.renderFlowchart();
        }

        this.lastMouseX = e.clientX;
        this.lastMouseY = e.clientY;
    }

    stopDrag(e) {
        if (this.isDraggingNode) {
            const nodeEl = this.flowchartCanvas.querySelector(`[data-node="${this.draggedNodeName}"]`);
            if(nodeEl) {
                nodeEl.style.zIndex = 10;
                nodeEl.style.cursor = 'pointer';
            }
        }
        this.isPanning = false;
        this.isDraggingNode = false;
        this.draggedNodeName = null;
        this.flowchartContainer.style.cursor = 'grab';
    }

    renderFolderTree() {
        this.folderTree.innerHTML = this.rootNodes.map(root => this.renderFolderNode(root)).join('');
        this.bindFolderEvents();
    }

    renderFolderNode(nodeName) {
        const node = this.hierarchyData[nodeName];
        if (!node || !this.matchesSearch(nodeName, node)) return '';

        const isExpanded = this.expandedNodes.has(nodeName);
        const hasChildren = node.children && node.children.length > 0;
        const childrenHtml = isExpanded && hasChildren ? `<div class="folder-children">${node.children.map(c => this.renderFolderNode(c)).join('')}</div>` : '';

        return `
            <div class="folder-item ${this.selectedNode === nodeName ? 'selected' : ''}" data-node="${nodeName}">
                <span class="folder-toggle">${hasChildren ? (isExpanded ? '▼' : '▶') : ''}</span>
                <span class="folder-icon">${this.getNodeEmoji(nodeName)}</span>
                <div class="folder-content">
                    <div class="folder-name">${nodeName}</div>
                    ${node.description ? `<div class="folder-description">${node.description}</div>` : ''}
                </div>
                ${hasChildren ? `<span class="folder-badge">${node.children.length}</span>` : ''}
            </div>
            ${childrenHtml}
        `;
    }

    bindFolderEvents() {
        this.folderTree.querySelectorAll('.folder-item').forEach(item => {
            item.addEventListener('click', e => {
                e.stopPropagation();
                const nodeName = item.dataset.node;
                if (e.target.classList.contains('folder-toggle')) {
                    this.toggleNodeExpansion(nodeName);
                } else {
                    this.selectNode(nodeName);
                }
            });
        });
    }

    toggleNodeExpansion(nodeName) {
        if (this.expandedNodes.has(nodeName)) {
            this.expandedNodes.delete(nodeName);
        } else {
            this.expandedNodes.add(nodeName);
        }
        this.renderFolderTree();
    }

    toggleAll(expand) {
        if (expand) {
            this.expandedNodes = new Set(Object.keys(this.hierarchyData));
        } else {
            this.expandedNodes = new Set(this.rootNodes);
        }
        this.renderCurrentView();
    }

    selectNode(nodeName) {
        this.selectedNode = nodeName;
        this.showNodeDetails(nodeName);
        this.renderCurrentView();
    }

    showNodeDetails(nodeName) {
        const node = this.hierarchyData[nodeName];
        if (!node) return;

        document.getElementById('details-title').innerHTML = `${this.getNodeEmoji(nodeName)} ${nodeName}`;
        document.getElementById('details-content').innerHTML = `
            <div class="detail-section">
                <div class="detail-title">📋 Basic Information</div>
                <p><strong>Level:</strong> ${node.level}</p>
                <p><strong>Parent:</strong> ${node.parent || 'None (Root)'}</p>
                <p><strong>Description:</strong> ${node.description || 'N/A'}</p>
            </div>
            ${node.children && node.children.length > 0 ? `
            <div class="detail-section">
                <div class="detail-title">👥 Children (${node.children.length})</div>
                <ul class="detail-list">${node.children.map(child => `<li>${this.getNodeEmoji(child)} ${child}</li>`).join('')}</ul>
            </div>` : ''}
            ${node.inherited_properties && node.inherited_properties.length > 0 ? `
            <div class="detail-section">
                <div class="detail-title">🏷️ Properties (${node.inherited_properties.length})</div>
                <ul class="detail-list">${node.inherited_properties.map(prop => `<li>${prop}</li>`).join('')}</ul>
            </div>` : ''}
        `;
        this.detailsPanel.classList.add('open');
    }

    closeDetailsPanel() {
        this.detailsPanel.classList.remove('open');
        this.selectedNode = null;
        this.renderCurrentView();
    }

    getFilteredNodes() {
        if (!this.searchTerm) {
            return Object.keys(this.hierarchyData);
        }
        const matched = new Set();
        const search = (name) => {
            if (matched.has(name)) return;
            const node = this.hierarchyData[name];
            if (!node) return;
            if (name.toLowerCase().includes(this.searchTerm) || (node.description && node.description.toLowerCase().includes(this.searchTerm))) {
                matched.add(name);
                let current = node;
                while (current.parent) {
                    matched.add(current.parent);
                    current = this.hierarchyData[current.parent];
                }
            }
        };
        Object.keys(this.hierarchyData).forEach(name => search(name));
        return [...matched];
    }

    matchesSearch(nodeName, node) {
        if (!this.searchTerm) return true;
        return nodeName.toLowerCase().includes(this.searchTerm) || (node.description && node.description.toLowerCase().includes(this.searchTerm));
    }

    getNodeEmoji(nodeName) {
        const map = {
            'entity': '⭐', 'physical_entity': '🌍', 'abstract_entity': '💡',
            'location': '📍', 'object': '📦', 'living_thing': '🌱', 'agent': '🤖',
            'event': '🎉', 'time': '⌛', 'property': '🏷️', 'unit': '📏',
            'animal': '🐾', 'plant': '🌳', 'person': '🧑', 'organization': '🏢',
            'place': '🗺️', 'city': '🏙️', 'country': '🏳️', 'continent': '🌎', 'planet': '🪐',
            'default': '📄'
        };
        const type = nodeName.split('(')[0].trim();
        return map[type] || map[this.hierarchyData[nodeName]?.parent] || map.default;
    }

    showError(message) {
        console.error(message);
        const errorEl = document.createElement('div');
        errorEl.className = 'error-message';
        errorEl.textContent = message;
        document.body.appendChild(errorEl);
        setTimeout(() => errorEl.remove(), 5000);
    }
}

document.addEventListener('DOMContentLoaded', () => new EnhancedHierarchyVisualizer());
    </script>
</body>
</html>