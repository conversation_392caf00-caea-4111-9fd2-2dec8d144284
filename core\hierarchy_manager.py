import json
from typing import Dict, List, Set, Optional, Any
from pathlib import Path

class HierarchyManager:
    """Manages semantic hierarchy and inheritance for the knowledge engine"""
    
    def __init__(self, schema_file: str = "hierarchy_schema.json"):
        self.schema_file = schema_file
        self.hierarchy = {}
        self.type_cache = {}  # Cache for faster lookups
        self.inheritance_cache = {}  # Cache inherited properties/actions
        self.load_hierarchy()
    
    def load_hierarchy(self) -> None:
        """Load hierarchy schema from JSON file"""
        try:
            schema_path = Path(self.schema_file)
            if schema_path.exists():
                with open(schema_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.hierarchy = data.get('hierarchy', {})
                    self._build_caches()
            else:
                raise FileNotFoundError(f"Hierarchy schema file not found: {schema_path}")
        except Exception as e:
            raise Exception(f"Failed to load hierarchy: {e}")
    
    def _build_caches(self) -> None:
        """Build lookup caches for performance"""
        self.type_cache.clear()
        self.inheritance_cache.clear()
        
        # Build type cache with all types and their levels
        for type_name, type_data in self.hierarchy.items():
            self.type_cache[type_name] = {
                'level': type_data.get('level', 0),
                'parent': type_data.get('parent'),
                'children': type_data.get('children', []),
                'description': type_data.get('description', '')
            }
    
    def get_type_info(self, type_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific type"""
        return self.type_cache.get(type_name)
    
    def is_valid_type(self, type_name: str) -> bool:
        """Check if a type exists in the hierarchy"""
        return type_name in self.hierarchy
    
    def get_parent(self, type_name: str) -> Optional[str]:
        """Get the parent type of a given type"""
        type_info = self.get_type_info(type_name)
        return type_info.get('parent') if type_info else None
    
    def get_children(self, type_name: str) -> List[str]:
        """Get all direct children of a type"""
        type_info = self.get_type_info(type_name)
        return type_info.get('children', []) if type_info else []
    
    def get_ancestors(self, type_name: str) -> List[str]:
        """Get all ancestor types (parent, grandparent, etc.)"""
        ancestors = []
        current = type_name
        
        while current:
            parent = self.get_parent(current)
            if parent:
                ancestors.append(parent)
                current = parent
            else:
                break
        
        return ancestors
    
    def get_descendants(self, type_name: str) -> List[str]:
        """Get all descendant types (children, grandchildren, etc.)"""
        descendants = []
        
        def collect_descendants(current_type: str):
            children = self.get_children(current_type)
            for child in children:
                descendants.append(child)
                collect_descendants(child)
        
        collect_descendants(type_name)
        return descendants
    
    def is_subtype_of(self, child_type: str, parent_type: str) -> bool:
        """Check if child_type is a subtype of parent_type"""
        if child_type == parent_type:
            return True
        
        ancestors = self.get_ancestors(child_type)
        return parent_type in ancestors
    
    def get_common_ancestor(self, type1: str, type2: str) -> Optional[str]:
        """Find the most specific common ancestor of two types"""
        if not (self.is_valid_type(type1) and self.is_valid_type(type2)):
            return None
        
        ancestors1 = [type1] + self.get_ancestors(type1)
        ancestors2 = [type2] + self.get_ancestors(type2)
        
        # Find common ancestors
        common = set(ancestors1) & set(ancestors2)
        
        if not common:
            return None
        
        # Return the most specific (lowest level) common ancestor
        common_with_levels = [(t, self.get_type_info(t)['level']) for t in common]
        return max(common_with_levels, key=lambda x: x[1])[0]
    
    def get_inherited_properties(self, type_name: str) -> List[str]:
        """Get all inherited properties for a type"""
        if type_name in self.inheritance_cache:
            return self.inheritance_cache[type_name].get('properties', [])
        
        properties = set()
        
        # Get properties from current type
        if type_name in self.hierarchy:
            properties.update(self.hierarchy[type_name].get('inherited_properties', []))
        
        # Get properties from all ancestors
        ancestors = self.get_ancestors(type_name)
        for ancestor in ancestors:
            if ancestor in self.hierarchy:
                properties.update(self.hierarchy[ancestor].get('inherited_properties', []))
        
        result = list(properties)
        
        # Cache the result
        if type_name not in self.inheritance_cache:
            self.inheritance_cache[type_name] = {}
        self.inheritance_cache[type_name]['properties'] = result
        
        return result
    
    def get_inherited_actions(self, type_name: str) -> List[str]:
        """Get all inherited actions for a type"""
        if type_name in self.inheritance_cache:
            return self.inheritance_cache[type_name].get('actions', [])
        
        actions = set()
        
        # Get actions from current type
        if type_name in self.hierarchy:
            actions.update(self.hierarchy[type_name].get('inherited_actions', []))
        
        # Get actions from all ancestors
        ancestors = self.get_ancestors(type_name)
        for ancestor in ancestors:
            if ancestor in self.hierarchy:
                actions.update(self.hierarchy[ancestor].get('inherited_actions', []))
        
        result = list(actions)
        
        # Cache the result
        if type_name not in self.inheritance_cache:
            self.inheritance_cache[type_name] = {}
        self.inheritance_cache[type_name]['actions'] = result
        
        return result
    
    def get_type_path(self, type_name: str) -> List[str]:
        """Get the full path from root to this type"""
        if not self.is_valid_type(type_name):
            return []
        
        path = [type_name]
        ancestors = self.get_ancestors(type_name)
        path.extend(reversed(ancestors))  # Reverse to get root-to-leaf order
        return path
    
    def get_type_level(self, type_name: str) -> int:
        """Get the hierarchy level of a type (0 = root)"""
        type_info = self.get_type_info(type_name)
        return type_info.get('level', -1) if type_info else -1
    
    def find_types_by_property(self, property_name: str) -> List[str]:
        """Find all types that have a specific inherited property"""
        matching_types = []
        
        for type_name in self.hierarchy.keys():
            inherited_props = self.get_inherited_properties(type_name)
            if property_name in inherited_props:
                matching_types.append(type_name)
        
        return matching_types
    
    def find_types_by_action(self, action_name: str) -> List[str]:
        """Find all types that can perform a specific inherited action"""
        matching_types = []
        
        for type_name in self.hierarchy.keys():
            inherited_actions = self.get_inherited_actions(type_name)
            if action_name in inherited_actions:
                matching_types.append(type_name)
        
        return matching_types
    
    def validate_type_compatibility(self, subject_type: str, action: str, object_type: str = None) -> Dict[str, Any]:
        """Validate if a subject type can perform an action on an object type"""
        result = {
            'valid': False,
            'reason': '',
            'suggestions': []
        }
        
        # Check if subject type exists
        if not self.is_valid_type(subject_type):
            result['reason'] = f"Unknown subject type: {subject_type}"
            return result
        
        # Check if subject can perform the action (through inheritance)
        subject_actions = self.get_inherited_actions(subject_type)
        if action not in subject_actions:
            result['reason'] = f"{subject_type} cannot perform action: {action}"
            # Suggest types that can perform this action
            capable_types = self.find_types_by_action(action)
            if capable_types:
                result['suggestions'] = f"Types that can {action}: {', '.join(capable_types)}"
            return result
        
        # If object type is specified, validate compatibility
        if object_type and not self.is_valid_type(object_type):
            result['reason'] = f"Unknown object type: {object_type}"
            return result
        
        result['valid'] = True
        result['reason'] = "Type compatibility validated through hierarchy"
        return result
    
    def get_hierarchy_statistics(self) -> Dict[str, Any]:
        """Get statistics about the hierarchy"""
        stats = {
            'total_types': len(self.hierarchy),
            'max_depth': max(self.get_type_level(t) for t in self.hierarchy.keys()),
            'types_by_level': {},
            'leaf_types': [],
            'root_types': []
        }
        
        # Count types by level
        for type_name in self.hierarchy.keys():
            level = self.get_type_level(type_name)
            if level not in stats['types_by_level']:
                stats['types_by_level'][level] = 0
            stats['types_by_level'][level] += 1
            
            # Identify leaf and root types
            if not self.get_children(type_name):
                stats['leaf_types'].append(type_name)
            if not self.get_parent(type_name):
                stats['root_types'].append(type_name)
        
        return stats
    
    def export_hierarchy_tree(self, max_depth: int = None) -> str:
        """Export hierarchy as a tree string for visualization"""
        
        def build_tree_recursive(type_name: str, depth: int = 0, prefix: str = "") -> str:
            if max_depth is not None and depth > max_depth:
                return ""
            
            tree_str = f"{prefix}{type_name}\n"
            children = self.get_children(type_name)
            
            for i, child in enumerate(children):
                is_last = i == len(children) - 1
                new_prefix = prefix + ("└── " if is_last else "├── ")
                tree_str += build_tree_recursive(child, depth + 1, new_prefix)
            
            return tree_str

        full_tree = ""
        root_types = self.get_hierarchy_statistics()['root_types']
        for root_type in root_types:
            full_tree += build_tree_recursive(root_type)

        return full_tree


# Example usage and testing
if __name__ == "__main__":
    print("--- Running HierarchyManager Test Script ---")
    try:
        # Correctly locate the schema file relative to the project root
        # This allows running the script directly for testing purposes
        project_root = Path(__file__).parent.parent
        schema_path = project_root / 'config' / 'hierarchy_schema.json'
        hm = HierarchyManager(str(schema_path))
        
        print("🌳 Hierarchy Manager Test")
        print("=" * 30)
        
        # Test basic functionality
        print(f"\n📊 Hierarchy Statistics:")
        stats = hm.get_hierarchy_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Test inheritance
        print(f"\n🧬 Inheritance for 'cat':")
        print(f"  Ancestors: {hm.get_ancestors('mammal')}")
        print(f"  Inherited Properties: {hm.get_inherited_properties('mammal')}")
        print(f"  Inherited Actions: {hm.get_inherited_actions('mammal')}")
        
        # Test type relationships
        print(f"\n🔗 Type Relationships:")
        print(f"  Is 'mammal' subtype of 'animal'? {hm.is_subtype_of('mammal', 'animal')}")
        print(f"  Is 'mammal' subtype of 'plant'? {hm.is_subtype_of('mammal', 'plant')}")
        print(f"  Common ancestor of 'mammal' and 'bird': {hm.get_common_ancestor('mammal', 'bird')}")
        
        # Test validation
        print(f"\n✅ Validation Tests:")
        validation = hm.validate_type_compatibility('mammal', 'move')
        print(f"  Can mammal move? {validation}")
        
        # Export tree
        print(f"\n🌳 Hierarchy Tree:")
        print(hm.export_hierarchy_tree(max_depth=5))
        
    except Exception as e:
        print(f"❌ Error: {e}")