#!/usr/bin/env python3
"""
Enhanced Parser for Complex Sentences
Implements parsing for conjunctions, relative clauses, and conditional statements
"""

import re
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from config import config
from logger import logger, ParseError

class SentenceType(Enum):
    """Types of sentences the parser can handle"""
    SIMPLE = "simple"  # Basic SVO/SV
    COMPOUND = "compound"  # Multiple clauses with conjunctions
    COMPLEX = "complex"  # Main clause + subordinate clause
    CONDITIONAL = "conditional"  # If-then statements
    RELATIVE = "relative"  # Sentences with relative clauses

@dataclass
class ParsedClause:
    """Represents a parsed clause with grammatical components"""
    subject: str
    verb: str
    object: Optional[str] = None
    clause_type: str = "main"
    modifiers: List[str] = None
    
    def __post_init__(self):
        if self.modifiers is None:
            self.modifiers = []

@dataclass
class ParsedSentence:
    """Represents a fully parsed sentence with multiple clauses"""
    sentence_type: SentenceType
    main_clause: ParsedClause
    subordinate_clauses: List[ParsedClause] = None
    conjunctions: List[str] = None
    conditional_structure: Optional[Dict[str, Any]] = None
    raw_text: str = ""
    
    def __post_init__(self):
        if self.subordinate_clauses is None:
            self.subordinate_clauses = []
        if self.conjunctions is None:
            self.conjunctions = []

class EnhancedSentenceParser:
    """Enhanced parser for complex sentence structures"""
    
    def __init__(self):
        # Basic patterns from original parser
        self.basic_patterns = {
            'svo': ['subject', 'verb', 'object'],
            'sv': ['subject', 'verb'],
            'svo_is': ['subject', 'verb', 'object']
        }
        
        # Conjunction words
        self.conjunctions = {
            'coordinating': ['and', 'but', 'or', 'nor', 'for', 'so', 'yet'],
            'subordinating': ['because', 'since', 'although', 'while', 'when', 'where', 'if', 'unless', 'until']
        }
        
        # Relative pronouns
        self.relative_pronouns = ['who', 'whom', 'whose', 'which', 'that', 'where', 'when', 'why']
        
        # Conditional patterns
        self.conditional_patterns = {
            'if_then': r'if\s+(.+?)\s+then\s+(.+)',
            'if_simple': r'if\s+(.+?)\s*,?\s*(.+)',
            'when_then': r'when\s+(.+?)\s+then\s+(.+)'
        }
        
        self.max_sentence_length = config.get('system.max_sentence_length', 20)  # Increased for complex sentences
    
    def parse(self, text: str) -> ParsedSentence:
        """Parse text into structured sentence representation"""
        try:
            # Preprocessing
            cleaned_text = self._preprocess_text(text)
            words = self._tokenize(cleaned_text)
            
            # Validate length
            if len(words) > self.max_sentence_length:
                raise ParseError(f"Sentence too long: {len(words)} words (max: {self.max_sentence_length})")
            
            if len(words) == 0:
                raise ParseError("Empty sentence")
            
            # Determine sentence type and parse accordingly
            sentence_type = self._identify_sentence_type(words, cleaned_text)
            
            if sentence_type == SentenceType.CONDITIONAL:
                return self._parse_conditional(cleaned_text, words)
            elif sentence_type == SentenceType.COMPOUND:
                return self._parse_compound(words, cleaned_text)
            elif sentence_type == SentenceType.COMPLEX:
                return self._parse_complex(words, cleaned_text)
            elif sentence_type == SentenceType.RELATIVE:
                return self._parse_relative(words, cleaned_text)
            else:
                return self._parse_simple(words, cleaned_text)
                
        except Exception as e:
            logger.error(f"Failed to parse sentence: '{text}'", exception=e)
            if isinstance(e, ParseError):
                raise
            else:
                raise ParseError(f"Parsing error: {str(e)}")
    
    def _preprocess_text(self, text: str) -> str:
        """Clean and normalize input text"""
        if not isinstance(text, str):
            raise ParseError(f"Input must be string, got {type(text)}")
        
        # Convert to lowercase and strip whitespace
        cleaned = text.lower().strip()
        
        # Remove extra whitespace but preserve sentence structure
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # Keep important punctuation for parsing
        # Remove only end punctuation
        cleaned = re.sub(r'[.!?]+$', '', cleaned)
        
        return cleaned
    
    def _tokenize(self, text: str) -> List[str]:
        """Split text into tokens while preserving important punctuation"""
        if not text:
            return []
        
        # Split on whitespace but keep commas as separate tokens
        tokens = re.findall(r'\w+|,', text)
        
        # Filter out empty tokens
        tokens = [token for token in tokens if token.strip()]
        
        return tokens
    
    def _identify_sentence_type(self, words: List[str], text: str) -> SentenceType:
        """Identify the type of sentence structure"""
        # Check for conditional patterns
        if any(word in words for word in ['if', 'when', 'unless']):
            return SentenceType.CONDITIONAL
        
        # Check for relative pronouns
        if any(word in words for word in self.relative_pronouns):
            return SentenceType.RELATIVE
        
        # Check for coordinating conjunctions
        if any(word in words for word in self.conjunctions['coordinating']):
            return SentenceType.COMPOUND
        
        # Check for subordinating conjunctions
        if any(word in words for word in self.conjunctions['subordinating']):
            return SentenceType.COMPLEX
        
        # Check for comma-separated clauses
        if ',' in words:
            return SentenceType.COMPOUND
        
        return SentenceType.SIMPLE
    
    def _parse_simple(self, words: List[str], text: str) -> ParsedSentence:
        """Parse simple SVO/SV sentences"""
        # Remove commas for simple parsing
        clean_words = [w for w in words if w != ',']
        
        # Try basic patterns
        if len(clean_words) == 3 and clean_words[1] == 'is':
            clause = ParsedClause(
                subject=clean_words[0],
                verb=clean_words[1],
                object=clean_words[2],
                clause_type="main"
            )
        elif len(clean_words) == 3:
            clause = ParsedClause(
                subject=clean_words[0],
                verb=clean_words[1],
                object=clean_words[2],
                clause_type="main"
            )
        elif len(clean_words) == 2:
            clause = ParsedClause(
                subject=clean_words[0],
                verb=clean_words[1],
                clause_type="main"
            )
        else:
            raise ParseError(f"Cannot parse simple sentence with {len(clean_words)} words")
        
        return ParsedSentence(
            sentence_type=SentenceType.SIMPLE,
            main_clause=clause,
            raw_text=text
        )
    
    def _parse_conditional(self, text: str, words: List[str]) -> ParsedSentence:
        """Parse conditional sentences (if-then, when-then)"""
        # Try different conditional patterns
        for pattern_name, pattern in self.conditional_patterns.items():
            match = re.search(pattern, text)
            if match:
                condition_text = match.group(1).strip()
                consequence_text = match.group(2).strip()
                
                # Parse condition and consequence as separate clauses
                try:
                    condition_words = self._tokenize(condition_text)
                    consequence_words = self._tokenize(consequence_text)
                    
                    condition_clause = self._parse_clause(condition_words, "condition")
                    consequence_clause = self._parse_clause(consequence_words, "consequence")
                    
                    conditional_structure = {
                        'type': pattern_name,
                        'condition': condition_clause,
                        'consequence': consequence_clause
                    }
                    
                    return ParsedSentence(
                        sentence_type=SentenceType.CONDITIONAL,
                        main_clause=consequence_clause,
                        subordinate_clauses=[condition_clause],
                        conditional_structure=conditional_structure,
                        raw_text=text
                    )
                    
                except Exception as e:
                    logger.debug(f"Failed to parse conditional with pattern {pattern_name}: {e}")
                    continue
        
        raise ParseError("Could not parse conditional sentence")
    
    def _parse_compound(self, words: List[str], text: str) -> ParsedSentence:
        """Parse compound sentences with coordinating conjunctions"""
        # Find conjunctions and split clauses
        conjunction_indices = []
        found_conjunctions = []
        
        for i, word in enumerate(words):
            if word in self.conjunctions['coordinating'] or word == ',':
                conjunction_indices.append(i)
                if word != ',':
                    found_conjunctions.append(word)
        
        if not conjunction_indices:
            raise ParseError("No conjunctions found in compound sentence")
        
        # Split into clauses
        clauses = []
        start_idx = 0
        
        for conj_idx in conjunction_indices:
            clause_words = words[start_idx:conj_idx]
            if clause_words:  # Skip empty clauses
                try:
                    clause = self._parse_clause(clause_words, "coordinate")
                    clauses.append(clause)
                except Exception as e:
                    logger.debug(f"Failed to parse clause: {clause_words}, error: {e}")
            start_idx = conj_idx + 1
        
        # Parse final clause
        if start_idx < len(words):
            final_words = words[start_idx:]
            if final_words:
                try:
                    clause = self._parse_clause(final_words, "coordinate")
                    clauses.append(clause)
                except Exception as e:
                    logger.debug(f"Failed to parse final clause: {final_words}, error: {e}")
        
        if not clauses:
            raise ParseError("No valid clauses found in compound sentence")
        
        # First clause is main, rest are subordinate
        main_clause = clauses[0]
        main_clause.clause_type = "main"
        subordinate_clauses = clauses[1:] if len(clauses) > 1 else []
        
        return ParsedSentence(
            sentence_type=SentenceType.COMPOUND,
            main_clause=main_clause,
            subordinate_clauses=subordinate_clauses,
            conjunctions=found_conjunctions,
            raw_text=text
        )
    
    def _parse_complex(self, words: List[str], text: str) -> ParsedSentence:
        """Parse complex sentences with subordinating conjunctions"""
        # Find subordinating conjunctions
        sub_conj_idx = None
        sub_conj = None
        
        for i, word in enumerate(words):
            if word in self.conjunctions['subordinating']:
                sub_conj_idx = i
                sub_conj = word
                break
        
        if sub_conj_idx is None:
            raise ParseError("No subordinating conjunction found")
        
        # Split into main and subordinate clauses
        if sub_conj_idx == 0:  # Subordinate clause first
            # Find where subordinate clause ends (usually at comma or end)
            comma_idx = None
            for i in range(sub_conj_idx + 1, len(words)):
                if words[i] == ',':
                    comma_idx = i
                    break
            
            if comma_idx:
                sub_words = words[sub_conj_idx + 1:comma_idx]
                main_words = words[comma_idx + 1:]
            else:
                # No comma, split roughly in half
                mid_point = len(words) // 2
                sub_words = words[sub_conj_idx + 1:mid_point]
                main_words = words[mid_point:]
        else:  # Main clause first
            main_words = words[:sub_conj_idx]
            sub_words = words[sub_conj_idx + 1:]
        
        try:
            main_clause = self._parse_clause(main_words, "main")
            sub_clause = self._parse_clause(sub_words, "subordinate")
            
            return ParsedSentence(
                sentence_type=SentenceType.COMPLEX,
                main_clause=main_clause,
                subordinate_clauses=[sub_clause],
                conjunctions=[sub_conj],
                raw_text=text
            )
            
        except Exception as e:
            raise ParseError(f"Failed to parse complex sentence: {e}")
    
    def _parse_relative(self, words: List[str], text: str) -> ParsedSentence:
        """Parse sentences with relative clauses"""
        # Find relative pronoun
        rel_pronoun_idx = None
        rel_pronoun = None
        
        for i, word in enumerate(words):
            if word in self.relative_pronouns:
                rel_pronoun_idx = i
                rel_pronoun = word
                break
        
        if rel_pronoun_idx is None:
            raise ParseError("No relative pronoun found")
        
        # Split into main clause and relative clause
        main_words = words[:rel_pronoun_idx]
        rel_words = words[rel_pronoun_idx + 1:]  # Skip the relative pronoun
        
        try:
            main_clause = self._parse_clause(main_words, "main")
            
            # For relative clause, the relative pronoun often acts as subject
            if rel_words:
                rel_clause = self._parse_clause(rel_words, "relative")
                # Modify to include relative pronoun as subject if needed
                if not rel_clause.subject or len(rel_clause.subject) == 0:
                    rel_clause.subject = rel_pronoun
            else:
                # Relative pronoun is the subject
                rel_clause = ParsedClause(
                    subject=rel_pronoun,
                    verb="",  # Will be filled if more words follow
                    clause_type="relative"
                )
            
            return ParsedSentence(
                sentence_type=SentenceType.RELATIVE,
                main_clause=main_clause,
                subordinate_clauses=[rel_clause],
                raw_text=text
            )
            
        except Exception as e:
            raise ParseError(f"Failed to parse relative clause: {e}")
    
    def _parse_clause(self, words: List[str], clause_type: str) -> ParsedClause:
        """Parse a single clause into subject-verb-object structure"""
        if not words:
            raise ParseError("Empty clause")
        
        # Remove commas
        clean_words = [w for w in words if w != ',']
        
        if len(clean_words) == 1:
            # Single word - assume it's a subject
            return ParsedClause(
                subject=clean_words[0],
                verb="",
                clause_type=clause_type
            )
        elif len(clean_words) == 2:
            # Subject-verb
            return ParsedClause(
                subject=clean_words[0],
                verb=clean_words[1],
                clause_type=clause_type
            )
        elif len(clean_words) == 3:
            # Subject-verb-object or subject-is-object
            return ParsedClause(
                subject=clean_words[0],
                verb=clean_words[1],
                object=clean_words[2],
                clause_type=clause_type
            )
        else:
            # More complex - take first as subject, second as verb, rest as object
            return ParsedClause(
                subject=clean_words[0],
                verb=clean_words[1],
                object=' '.join(clean_words[2:]),
                clause_type=clause_type,
                modifiers=clean_words[3:] if len(clean_words) > 3 else []
            )
    
    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get parser statistics and capabilities"""
        return {
            'supported_sentence_types': [t.value for t in SentenceType],
            'coordinating_conjunctions': self.conjunctions['coordinating'],
            'subordinating_conjunctions': self.conjunctions['subordinating'],
            'relative_pronouns': self.relative_pronouns,
            'conditional_patterns': list(self.conditional_patterns.keys()),
            'max_sentence_length': self.max_sentence_length
        }
    
    def validate_parsed_sentence(self, parsed: ParsedSentence) -> Tuple[bool, str]:
        """Validate the structure of a parsed sentence"""
        try:
            # Check main clause
            if not parsed.main_clause or not parsed.main_clause.subject:
                return False, "Missing main clause or subject"
            
            # Check sentence type consistency
            if parsed.sentence_type == SentenceType.CONDITIONAL:
                if not parsed.conditional_structure:
                    return False, "Conditional sentence missing conditional structure"
            
            elif parsed.sentence_type == SentenceType.COMPOUND:
                if not parsed.subordinate_clauses:
                    return False, "Compound sentence missing coordinate clauses"
            
            elif parsed.sentence_type == SentenceType.COMPLEX:
                if not parsed.subordinate_clauses or not parsed.conjunctions:
                    return False, "Complex sentence missing subordinate clause or conjunction"
            
            return True, "Valid sentence structure"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"