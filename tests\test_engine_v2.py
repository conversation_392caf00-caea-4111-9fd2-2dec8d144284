#!/usr/bin/env python3
"""
Comprehensive test suite for Knowledge Engine V2
"""

import sys
import time
import json
from typing import Dict, List, Any
from knowledge_engine_v2 import KnowledgeEngineV2
from logger import logger

class TestSuite:
    """Comprehensive test suite for the knowledge engine"""
    
    def __init__(self):
        self.engine = None
        self.test_results = []
        self.performance_metrics = []
    
    def setup(self) -> bool:
        """Setup test environment"""
        try:
            print("🔧 Setting up test environment...")
            self.engine = KnowledgeEngineV2()
            print("✅ Test environment ready\n")
            return True
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def run_sentence_validation_tests(self) -> Dict[str, Any]:
        """Test sentence validation functionality"""
        print("📝 Running Sentence Validation Tests")
        print("=====================================")
        
        test_cases = [
            # Valid sentences
            {"sentence": "cat chase mouse", "expected": True, "description": "Basic SVO"},
            {"sentence": "dog bark", "expected": True, "description": "Simple SV"},
            {"sentence": "cat sleep", "expected": True, "description": "Animal action"},
            {"sentence": "mouse eat food", "expected": True, "description": "Eating action"},
            
            # Invalid sentences
            {"sentence": "table chase cat", "expected": False, "description": "Object as subject"},
            {"sentence": "cat unknown_verb", "expected": False, "description": "Unknown verb"},
            {"sentence": "unknown_animal sleep", "expected": False, "description": "Unknown subject"},
            {"sentence": "cat eat table", "expected": False, "description": "Wrong object type"},
            {"sentence": "", "expected": False, "description": "Empty sentence"},
            {"sentence": "cat", "expected": False, "description": "Incomplete sentence"},
        ]
        
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            sentence = test_case["sentence"]
            expected = test_case["expected"]
            description = test_case["description"]
            
            start_time = time.time()
            result = self.engine.process_sentence(sentence)
            processing_time = time.time() - start_time
            
            success = result['success'] == expected
            status = "✅" if success else "❌"
            
            print(f"{status} {description}: '{sentence}'")
            if not success:
                print(f"   Expected: {expected}, Got: {result['success']}")
                print(f"   Reason: {result.get('message', 'Unknown')}")
                failed += 1
            else:
                passed += 1
            
            if not result['success'] and result.get('suggestions'):
                print(f"   💡 Suggestions: {', '.join(result['suggestions'])}")
            
            # Record performance
            self.performance_metrics.append({
                'test_type': 'sentence_validation',
                'sentence': sentence,
                'processing_time': processing_time
            })
        
        print(f"\n📊 Sentence Validation Results: {passed} passed, {failed} failed\n")
        return {'passed': passed, 'failed': failed, 'total': len(test_cases)}
    
    def run_question_answering_tests(self) -> Dict[str, Any]:
        """Test question answering functionality"""
        print("❓ Running Question Answering Tests")
        print("===================================")
        
        test_cases = [
            {
                "question": "what can cat do?",
                "expected_contains": ["chase", "sleep"],
                "description": "Cat abilities"
            },
            {
                "question": "what is dog?",
                "expected_contains": ["animal"],
                "description": "Dog definition"
            },
            {
                "question": "what can mouse do?",
                "expected_contains": ["eat", "hide"],
                "description": "Mouse abilities"
            },
            {
                "question": "what is food?",
                "expected_contains": ["food"],
                "description": "Food definition"
            },
            {
                "question": "what can unknown_animal do?",
                "expected_contains": ["don't know"],
                "description": "Unknown subject"
            },
            {
                "question": "who is cat?",
                "expected_contains": ["animal"],
                "description": "Who question"
            }
        ]
        
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            question = test_case["question"]
            expected_contains = test_case["expected_contains"]
            description = test_case["description"]
            
            start_time = time.time()
            result = self.engine.answer_question(question)
            processing_time = time.time() - start_time
            
            if result['success']:
                answer = result['answer'].lower()
                contains_expected = any(expected in answer for expected in expected_contains)
                
                status = "✅" if contains_expected else "❌"
                print(f"{status} {description}")
                print(f"   Q: {question}")
                print(f"   A: {result['answer']}")
                
                if contains_expected:
                    passed += 1
                else:
                    failed += 1
                    print(f"   Expected to contain: {expected_contains}")
            else:
                print(f"❌ {description} - Failed to get answer")
                print(f"   Error: {result.get('message', 'Unknown error')}")
                failed += 1
            
            # Record performance
            self.performance_metrics.append({
                'test_type': 'question_answering',
                'question': question,
                'processing_time': processing_time
            })
            
            print()
        
        print(f"📊 Question Answering Results: {passed} passed, {failed} failed\n")
        return {'passed': passed, 'failed': failed, 'total': len(test_cases)}
    
    def run_learning_tests(self) -> Dict[str, Any]:
        """Test learning functionality"""
        print("📚 Running Learning Tests")
        print("=========================")
        
        test_cases = [
            {
                "subject": "cat",
                "fact_type": "action",
                "fact_value": "jump",
                "description": "Learn new action"
            },
            {
                "subject": "dog",
                "fact_type": "property",
                "fact_value": "loyal",
                "description": "Learn new property"
            },
            {
                "subject": "bird",
                "fact_type": "action",
                "fact_value": "fly",
                "description": "Learn about new subject"
            }
        ]
        
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            subject = test_case["subject"]
            fact_type = test_case["fact_type"]
            fact_value = test_case["fact_value"]
            description = test_case["description"]
            
            # Learn the fact
            start_time = time.time()
            learn_result = self.engine.learn_fact(subject, fact_type, fact_value)
            processing_time = time.time() - start_time
            
            if learn_result['success']:
                print(f"✅ {description}: {learn_result['message']}")
                
                # Verify learning by asking a question
                if fact_type == "action":
                    verify_result = self.engine.answer_question(f"what can {subject} do?")
                    if verify_result['success'] and fact_value in verify_result['answer']:
                        print(f"   ✅ Verification: {verify_result['answer']}")
                        passed += 1
                    else:
                        print(f"   ❌ Verification failed: {verify_result.get('answer', 'No answer')}")
                        failed += 1
                else:
                    passed += 1  # For non-action facts, just check if learning succeeded
            else:
                print(f"❌ {description}: {learn_result.get('message', 'Unknown error')}")
                failed += 1
            
            # Record performance
            self.performance_metrics.append({
                'test_type': 'learning',
                'subject': subject,
                'processing_time': processing_time
            })
            
            print()
        
        print(f"📊 Learning Results: {passed} passed, {failed} failed\n")
        return {'passed': passed, 'failed': failed, 'total': len(test_cases)}
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Test performance characteristics"""
        print("⚡ Running Performance Tests")
        print("============================")
        
        # Test sentence processing speed
        sentences = ["cat chase mouse"] * 100
        start_time = time.time()
        
        for sentence in sentences:
            self.engine.process_sentence(sentence)
        
        total_time = time.time() - start_time
        avg_time = total_time / len(sentences)
        
        print(f"✅ Processed {len(sentences)} sentences in {total_time:.3f}s")
        print(f"✅ Average processing time: {avg_time*1000:.2f}ms per sentence")
        
        # Test question answering speed
        questions = ["what can cat do?"] * 50
        start_time = time.time()
        
        for question in questions:
            self.engine.answer_question(question)
        
        total_time = time.time() - start_time
        avg_time = total_time / len(questions)
        
        print(f"✅ Answered {len(questions)} questions in {total_time:.3f}s")
        print(f"✅ Average answering time: {avg_time*1000:.2f}ms per question\n")
        
        return {
            'sentence_processing_avg_ms': avg_time * 1000,
            'question_answering_avg_ms': avg_time * 1000
        }
    
    def run_error_handling_tests(self) -> Dict[str, Any]:
        """Test error handling and edge cases"""
        print("🛡️ Running Error Handling Tests")
        print("================================")
        
        test_cases = [
            {"input": "", "type": "sentence", "description": "Empty input"},
            {"input": "   ", "type": "sentence", "description": "Whitespace only"},
            {"input": "a" * 1000, "type": "sentence", "description": "Very long input"},
            {"input": "cat 123 mouse", "type": "sentence", "description": "Numbers in sentence"},
            {"input": "what is ?", "type": "question", "description": "Malformed question"},
            {"input": "???", "type": "question", "description": "Invalid question format"},
        ]
        
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            input_text = test_case["input"]
            input_type = test_case["type"]
            description = test_case["description"]
            
            try:
                if input_type == "sentence":
                    result = self.engine.process_sentence(input_text)
                else:
                    result = self.engine.answer_question(input_text)
                
                # Should handle gracefully without crashing
                print(f"✅ {description}: Handled gracefully")
                if not result['success']:
                    print(f"   Message: {result.get('message', 'No message')}")
                passed += 1
                
            except Exception as e:
                print(f"❌ {description}: Crashed with {type(e).__name__}: {e}")
                failed += 1
        
        print(f"\n📊 Error Handling Results: {passed} passed, {failed} failed\n")
        return {'passed': passed, 'failed': failed, 'total': len(test_cases)}
    
    def generate_performance_report(self) -> None:
        """Generate detailed performance report"""
        if not self.performance_metrics:
            return
        
        print("📈 Performance Report")
        print("====================")
        
        # Group by test type
        by_type = {}
        for metric in self.performance_metrics:
            test_type = metric['test_type']
            if test_type not in by_type:
                by_type[test_type] = []
            by_type[test_type].append(metric['processing_time'])
        
        for test_type, times in by_type.items():
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            
            print(f"\n{test_type.replace('_', ' ').title()}:")
            print(f"  Average: {avg_time*1000:.2f}ms")
            print(f"  Min: {min_time*1000:.2f}ms")
            print(f"  Max: {max_time*1000:.2f}ms")
            print(f"  Samples: {len(times)}")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run complete test suite"""
        if not self.setup():
            return {'success': False, 'message': 'Setup failed'}
        
        print("🧪 Knowledge Engine V2 - Comprehensive Test Suite")
        print("=" * 50)
        print()
        
        # Run all test categories
        sentence_results = self.run_sentence_validation_tests()
        question_results = self.run_question_answering_tests()
        learning_results = self.run_learning_tests()
        performance_results = self.run_performance_tests()
        error_results = self.run_error_handling_tests()
        
        # Generate reports
        self.generate_performance_report()
        
        # Calculate overall results
        total_passed = (sentence_results['passed'] + question_results['passed'] + 
                       learning_results['passed'] + error_results['passed'])
        total_failed = (sentence_results['failed'] + question_results['failed'] + 
                       learning_results['failed'] + error_results['failed'])
        total_tests = total_passed + total_failed
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print("\n🏆 Final Results")
        print("===============")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {total_passed}")
        print(f"Failed: {total_failed}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Get engine status
        status = self.engine.get_engine_status()
        print(f"\n📊 Engine Statistics:")
        print(f"Sentences Processed: {status['statistics']['sentences_processed']}")
        print(f"Questions Answered: {status['statistics']['questions_answered']}")
        print(f"Facts Learned: {status['statistics']['facts_learned']}")
        
        return {
            'success': True,
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failed,
            'success_rate': success_rate,
            'performance': performance_results,
            'engine_status': status
        }

def interactive_mode():
    """Interactive testing mode"""
    print("🎮 Interactive Mode - Knowledge Engine V2")
    print("==========================================\n")
    
    try:
        engine = KnowledgeEngineV2()
        print("Engine initialized successfully!\n")
        
        while True:
            print("Options:")
            print("1. Test sentence")
            print("2. Ask question")
            print("3. Learn fact")
            print("4. Show engine status")
            print("5. Export knowledge")
            print("6. Exit")
            
            choice = input("\nChoose option (1-6): ").strip()
            
            if choice == '1':
                sentence = input("Enter sentence: ").strip()
                if sentence:
                    result = engine.process_sentence(sentence)
                    status = "✅" if result['success'] else "❌"
                    print(f"{status} {result['message']}")
                    if result.get('suggestions'):
                        print("Suggestions:", ', '.join(result['suggestions']))
            
            elif choice == '2':
                question = input("Enter question: ").strip()
                if question:
                    result = engine.answer_question(question)
                    if result['success']:
                        print(f"Answer: {result['answer']}")
                    else:
                        print(f"❌ {result['message']}")
            
            elif choice == '3':
                subject = input("Subject: ").strip()
                fact_type = input("Fact type (action/property): ").strip()
                fact_value = input("Fact value: ").strip()
                
                if all([subject, fact_type, fact_value]):
                    result = engine.learn_fact(subject, fact_type, fact_value)
                    status = "✅" if result['success'] else "❌"
                    print(f"{status} {result['message']}")
            
            elif choice == '4':
                status = engine.get_engine_status()
                print("\n📊 Engine Status:")
                print(json.dumps(status, indent=2))
            
            elif choice == '5':
                filename = input("Export filename (default: knowledge_export.json): ").strip()
                if not filename:
                    filename = "knowledge_export.json"
                result = engine.export_knowledge(filename)
                status = "✅" if result['success'] else "❌"
                print(f"{status} {result['message']}")
            
            elif choice == '6':
                print("Goodbye! 👋")
                break
            
            else:
                print("Invalid option. Please choose 1-6.")
            
            print()
    
    except Exception as e:
        print(f"❌ Interactive mode failed: {e}")

def main():
    """Main test runner"""
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            # Run automated tests
            suite = TestSuite()
            results = suite.run_all_tests()
            
            # Exit with appropriate code
            sys.exit(0 if results.get('success') and results.get('failed', 1) == 0 else 1)
        
        elif sys.argv[1] == 'interactive':
            # Run interactive mode
            interactive_mode()
        
        else:
            print("Usage: python test_engine_v2.py [test|interactive]")
            sys.exit(1)
    else:
        # Default to interactive mode
        interactive_mode()

if __name__ == "__main__":
    main()