#!/usr/bin/env python3
"""
Debug script for hierarchy system
"""

import sys
import traceback

def test_basic_imports():
    """Test basic imports"""
    try:
        print("🔍 Testing imports...")
        
        from hierarchy_manager import HierarchyManager
        print("✅ HierarchyManager imported")
        
        from knowledge_base_v3 import HierarchyAwareKnowledgeBase
        print("✅ HierarchyAwareKnowledgeBase imported")
        
        from knowledge_engine_v3 import HierarchyAwareKnowledgeEngine
        print("✅ HierarchyAwareKnowledgeEngine imported")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_hierarchy_manager():
    """Test hierarchy manager"""
    try:
        print("\n🌳 Testing HierarchyManager...")
        
        from hierarchy_manager import HierarchyManager
        hm = HierarchyManager()
        
        print(f"✅ HierarchyManager created")
        print(f"   Valid types: {list(hm.type_cache.keys())[:5]}...")
        
        # Test basic functionality
        is_valid = hm.is_valid_type('mammal')
        print(f"   Is 'mammal' valid? {is_valid}")
        
        parent = hm.get_parent('mammal')
        print(f"   Parent of 'mammal': {parent}")
        
        return True
    except Exception as e:
        print(f"❌ HierarchyManager test failed: {e}")
        traceback.print_exc()
        return False

def test_knowledge_base():
    """Test knowledge base"""
    try:
        print("\n🧠 Testing HierarchyAwareKnowledgeBase...")
        
        from knowledge_base_v3 import HierarchyAwareKnowledgeBase
        kb = HierarchyAwareKnowledgeBase()
        
        print(f"✅ Knowledge base created")
        print(f"   Total concepts: {len(kb.knowledge)}")
        
        # Test adding a concept
        result = kb.add_concept('test_cat', {
            'type': 'mammal',
            'actions': ['meow'],
            'properties': ['furry'],
            'meaning': 'a test cat'
        })
        
        print(f"   Added concept result: {result['success']}")
        
        # Test getting concept
        concept = kb.get_concept('test_cat')
        if concept:
            print(f"   Retrieved concept type: {concept.get('type')}")
            print(f"   Inherited actions: {concept.get('inherited_actions', [])}")
        
        return True
    except Exception as e:
        print(f"❌ Knowledge base test failed: {e}")
        traceback.print_exc()
        return False

def test_engine():
    """Test knowledge engine"""
    try:
        print("\n🚀 Testing HierarchyAwareKnowledgeEngine...")
        
        from knowledge_engine_v3 import HierarchyAwareKnowledgeEngine
        engine = HierarchyAwareKnowledgeEngine()
        
        print(f"✅ Engine created")
        
        # Test validation
        result = engine.validate_sentence('cat runs')
        print(f"   Validation of 'cat runs': {result}")
        
        return True
    except Exception as e:
        print(f"❌ Engine test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔧 Hierarchy System Debug")
    print("=" * 30)
    
    tests = [
        test_basic_imports,
        test_hierarchy_manager,
        test_knowledge_base,
        test_engine
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed")

if __name__ == "__main__":
    main()