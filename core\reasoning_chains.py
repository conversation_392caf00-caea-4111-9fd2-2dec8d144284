#!/usr/bin/env python3
"""
Symbolic Reasoning Chains for Hierarchy-Aware Knowledge Engine

This module implements multi-step inference chains that can traverse
the knowledge hierarchy to answer complex questions through symbolic reasoning.

Example: "Can cats fly?"
1. Check: cat -> is_a -> mammal
2. Check: mammals -> cannot -> fly
3. Conclude: cats cannot fly

Features:
- Multi-step inference chains
- Hierarchy traversal
- Contradiction detection during reasoning
- Confidence propagation
- Explanation generation
- Reasoning depth limits
"""

import json
import time
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum


class ReasoningType(Enum):
    """Types of reasoning steps"""
    DIRECT_LOOKUP = "direct_lookup"
    HIERARCHY_TRAVERSAL = "hierarchy_traversal"
    PROPERTY_INHERITANCE = "property_inheritance"
    NEGATION_INFERENCE = "negation_inference"
    CONDITIONAL_APPLICATION = "conditional_application"
    CONTRADICTION_RESOLUTION = "contradiction_resolution"


class StepResult(Enum):
    """Results of reasoning steps"""
    SUCCESS = "success"
    FAILURE = "failure"
    CONTRADICTION = "contradiction"
    INSUFFICIENT_INFO = "insufficient_info"
    DEPTH_LIMIT = "depth_limit"


@dataclass
class ReasoningStep:
    """Represents a single step in a reasoning chain"""
    step_number: int
    reasoning_type: ReasoningType
    query: str
    result: StepResult
    evidence: List[Dict[str, Any]]
    confidence: float
    explanation: str
    timestamp: float
    
    def __post_init__(self):
        if not hasattr(self, 'timestamp') or self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class ReasoningChain:
    """Represents a complete reasoning chain"""
    query: str
    steps: List[ReasoningStep]
    final_answer: bool
    overall_confidence: float
    total_time: float
    explanation: str
    contradictions_found: List[Dict[str, Any]]
    
    def add_step(self, step: ReasoningStep):
        """Add a step to the reasoning chain"""
        self.steps.append(step)
    
    def get_step_count(self) -> int:
        """Get the number of steps in the chain"""
        return len(self.steps)
    
    def get_evidence_trail(self) -> List[str]:
        """Get a list of evidence used in reasoning"""
        evidence_trail = []
        for step in self.steps:
            for evidence in step.evidence:
                evidence_trail.append(str(evidence))
        return evidence_trail


class ReasoningChainEngine:
    """Engine for performing symbolic reasoning chains"""
    
    def __init__(self, knowledge_base=None, hierarchy_manager=None, 
                 conditional_logic=None, contradiction_detector=None,
                 max_depth: int = 10, min_confidence: float = 0.1):
        self.knowledge_base = knowledge_base
        self.hierarchy_manager = hierarchy_manager
        self.conditional_logic = conditional_logic
        self.contradiction_detector = contradiction_detector
        self.max_depth = max_depth
        self.min_confidence = min_confidence
        
        # Statistics
        self.total_chains = 0
        self.successful_chains = 0
        self.failed_chains = 0
        self.average_chain_length = 0.0
        self.reasoning_cache = {}  # Cache for repeated queries
    
    def reason(self, query: str, context: Optional[Dict] = None) -> ReasoningChain:
        """Perform symbolic reasoning to answer a query"""
        start_time = time.time()
        
        # Check cache first
        cache_key = self._get_cache_key(query, context)
        if cache_key in self.reasoning_cache:
            cached_result = self.reasoning_cache[cache_key]
            cached_result.total_time = time.time() - start_time
            return cached_result
        
        # Initialize reasoning chain
        chain = ReasoningChain(
            query=query,
            steps=[],
            final_answer=False,
            overall_confidence=0.0,
            total_time=0.0,
            explanation="",
            contradictions_found=[]
        )
        
        try:
            # Parse the query to understand what we're looking for
            parsed_query = self._parse_query(query)
            
            if not parsed_query:
                chain.explanation = "Could not parse query"
                chain.final_answer = False
                chain.overall_confidence = 0.0
                return chain
            
            # Perform reasoning steps
            self._perform_reasoning_steps(chain, parsed_query, context)
            
            # Calculate final confidence and answer
            self._finalize_chain(chain)
            
            # Cache the result
            self.reasoning_cache[cache_key] = chain
            
        except Exception as e:
            # Handle errors gracefully
            error_step = ReasoningStep(
                step_number=len(chain.steps) + 1,
                reasoning_type=ReasoningType.DIRECT_LOOKUP,
                query=query,
                result=StepResult.FAILURE,
                evidence=[{"error": str(e)}],
                confidence=0.0,
                explanation=f"Error during reasoning: {str(e)}",
                timestamp=time.time()
            )
            chain.add_step(error_step)
            chain.final_answer = False
            chain.overall_confidence = 0.0
            chain.explanation = f"Reasoning failed: {str(e)}"
        
        finally:
            chain.total_time = time.time() - start_time
            self._update_statistics(chain)
        
        return chain
    
    def _parse_query(self, query: str) -> Optional[Dict[str, Any]]:
        """Parse a query to extract subject, predicate, and object"""
        query = query.strip().lower()
        
        # Handle "can X do Y?" questions
        if query.startswith("can ") and " do " in query:
            parts = query[4:].split(" do ")
            if len(parts) == 2:
                return {
                    "type": "ability",
                    "subject": parts[0].strip(),
                    "action": parts[1].strip().rstrip("?")
                }
        
        # Handle "can X Y?" questions
        elif query.startswith("can "):
            parts = query[4:].split()
            if len(parts) >= 2:
                subject = parts[0]
                action = " ".join(parts[1:]).rstrip("?")
                return {
                    "type": "ability",
                    "subject": subject,
                    "action": action
                }
        
        # Handle "is X Y?" questions
        elif query.startswith("is "):
            parts = query[3:].split()
            if len(parts) >= 2:
                subject = parts[0]
                predicate = " ".join(parts[1:]).rstrip("?")
                return {
                    "type": "property",
                    "subject": subject,
                    "predicate": predicate
                }
        
        # Handle "does X Y?" questions
        elif query.startswith("does "):
            parts = query[5:].split()
            if len(parts) >= 2:
                subject = parts[0]
                action = " ".join(parts[1:]).rstrip("?")
                return {
                    "type": "action",
                    "subject": subject,
                    "action": action
                }
        
        return None
    
    def _perform_reasoning_steps(self, chain: ReasoningChain, 
                                parsed_query: Dict[str, Any], 
                                context: Optional[Dict] = None):
        """Perform the actual reasoning steps"""
        query_type = parsed_query["type"]
        subject = parsed_query["subject"]
        
        if query_type == "ability":
            action = parsed_query["action"]
            self._reason_about_ability(chain, subject, action, context)
        elif query_type == "property":
            predicate = parsed_query["predicate"]
            self._reason_about_property(chain, subject, predicate, context)
        elif query_type == "action":
            action = parsed_query["action"]
            self._reason_about_action(chain, subject, action, context)
    
    def _reason_about_ability(self, chain: ReasoningChain, subject: str, 
                             action: str, context: Optional[Dict] = None):
        """Reason about whether a subject can perform an action"""
        step_num = 1
        
        # Step 1: Direct lookup
        direct_result = self._direct_lookup_step(chain, step_num, subject, "can", action)
        if direct_result != StepResult.INSUFFICIENT_INFO:
            return
        
        step_num += 1
        
        # Step 2: Check hierarchy for inherited abilities
        hierarchy_result = self._hierarchy_traversal_step(chain, step_num, subject, "can", action)
        if hierarchy_result != StepResult.INSUFFICIENT_INFO:
            return
        
        step_num += 1
        
        # Step 3: Check for general rules about the subject's type
        type_result = self._check_type_abilities(chain, step_num, subject, action)
        if type_result != StepResult.INSUFFICIENT_INFO:
            return
        
        # If we get here, we don't have enough information
        insufficient_step = ReasoningStep(
            step_number=step_num,
            reasoning_type=ReasoningType.DIRECT_LOOKUP,
            query=f"Can {subject} {action}?",
            result=StepResult.INSUFFICIENT_INFO,
            evidence=[],
            confidence=0.0,
            explanation=f"Insufficient information to determine if {subject} can {action}",
            timestamp=time.time()
        )
        chain.add_step(insufficient_step)
    
    def _reason_about_property(self, chain: ReasoningChain, subject: str, 
                              predicate: str, context: Optional[Dict] = None):
        """Reason about whether a subject has a property"""
        step_num = 1
        
        # Step 1: Direct lookup
        direct_result = self._direct_lookup_step(chain, step_num, subject, "is", predicate)
        if direct_result != StepResult.INSUFFICIENT_INFO:
            return
        
        step_num += 1
        
        # Step 2: Check hierarchy for inherited properties
        hierarchy_result = self._hierarchy_traversal_step(chain, step_num, subject, "is", predicate)
        if hierarchy_result != StepResult.INSUFFICIENT_INFO:
            return
        
        # If we get here, we don't have enough information
        insufficient_step = ReasoningStep(
            step_number=step_num,
            reasoning_type=ReasoningType.DIRECT_LOOKUP,
            query=f"Is {subject} {predicate}?",
            result=StepResult.INSUFFICIENT_INFO,
            evidence=[],
            confidence=0.0,
            explanation=f"Insufficient information to determine if {subject} is {predicate}",
            timestamp=time.time()
        )
        chain.add_step(insufficient_step)
    
    def _reason_about_action(self, chain: ReasoningChain, subject: str, 
                            action: str, context: Optional[Dict] = None):
        """Reason about whether a subject performs an action"""
        # For now, treat actions similar to abilities
        self._reason_about_ability(chain, subject, action, context)
    
    def _direct_lookup_step(self, chain: ReasoningChain, step_num: int, 
                           subject: str, predicate: str, obj: str) -> StepResult:
        """Perform a direct lookup in the knowledge base"""
        evidence = []
        confidence = 0.0
        result = StepResult.INSUFFICIENT_INFO
        explanation = f"Looking for direct fact: {subject} {predicate} {obj}"
        
        # Simulate knowledge base lookup
        if self.knowledge_base:
            # This would be replaced with actual knowledge base query
            facts = self._query_knowledge_base(subject, predicate, obj)
            if facts:
                evidence = facts
                confidence = 0.9
                result = StepResult.SUCCESS
                explanation = f"Found direct fact: {subject} {predicate} {obj}"
        
        step = ReasoningStep(
            step_number=step_num,
            reasoning_type=ReasoningType.DIRECT_LOOKUP,
            query=f"{subject} {predicate} {obj}",
            result=result,
            evidence=evidence,
            confidence=confidence,
            explanation=explanation,
            timestamp=time.time()
        )
        chain.add_step(step)
        return result
    
    def _hierarchy_traversal_step(self, chain: ReasoningChain, step_num: int,
                                 subject: str, predicate: str, obj: str) -> StepResult:
        """Traverse the hierarchy to find inherited properties"""
        evidence = []
        confidence = 0.0
        result = StepResult.INSUFFICIENT_INFO
        explanation = f"Checking hierarchy for {subject} -> {predicate} -> {obj}"
        
        if self.hierarchy_manager:
            # Get parent types of the subject
            parents = self._get_parent_types(subject)
            
            for parent in parents:
                parent_facts = self._query_knowledge_base(parent, predicate, obj)
                if parent_facts:
                    evidence.extend(parent_facts)
                    confidence = max(confidence, 0.8)  # Slightly lower confidence for inherited
                    result = StepResult.SUCCESS
                    explanation = f"Found inherited property: {parent} {predicate} {obj}, inherited by {subject}"
                    break
        
        step = ReasoningStep(
            step_number=step_num,
            reasoning_type=ReasoningType.HIERARCHY_TRAVERSAL,
            query=f"{subject} inherits {predicate} {obj}",
            result=result,
            evidence=evidence,
            confidence=confidence,
            explanation=explanation,
            timestamp=time.time()
        )
        chain.add_step(step)
        return result
    
    def _check_type_abilities(self, chain: ReasoningChain, step_num: int,
                             subject: str, action: str) -> StepResult:
        """Check if the subject's type has general rules about the action"""
        evidence = []
        confidence = 0.0
        result = StepResult.INSUFFICIENT_INFO
        explanation = f"Checking type-based rules for {subject} and {action}"
        
        # Get the type of the subject
        subject_types = self._get_parent_types(subject)
        
        # Check for negative rules (cannot do)
        for subject_type in subject_types:
            negative_facts = self._query_knowledge_base(subject_type, "cannot", action)
            if negative_facts:
                evidence.extend(negative_facts)
                confidence = 0.8
                result = StepResult.SUCCESS
                explanation = f"Found negative rule: {subject_type} cannot {action}, therefore {subject} cannot {action}"
                break
        
        step = ReasoningStep(
            step_number=step_num,
            reasoning_type=ReasoningType.PROPERTY_INHERITANCE,
            query=f"{subject} type rules for {action}",
            result=result,
            evidence=evidence,
            confidence=confidence,
            explanation=explanation,
            timestamp=time.time()
        )
        chain.add_step(step)
        return result
    
    def _finalize_chain(self, chain: ReasoningChain):
        """Calculate final answer and confidence for the reasoning chain"""
        if not chain.steps:
            chain.final_answer = False
            chain.overall_confidence = 0.0
            chain.explanation = "No reasoning steps performed"
            return
        
        # Calculate overall confidence as weighted average
        total_confidence = 0.0
        successful_steps = 0
        
        for step in chain.steps:
            if step.result == StepResult.SUCCESS:
                total_confidence += step.confidence
                successful_steps += 1
        
        if successful_steps > 0:
            chain.overall_confidence = total_confidence / successful_steps
            chain.final_answer = True
            
            # Check for contradictions
            if any(step.result == StepResult.CONTRADICTION for step in chain.steps):
                chain.final_answer = False
                chain.explanation = "Contradictory evidence found"
            else:
                chain.explanation = f"Reasoning successful with {successful_steps} supporting steps"
        else:
            chain.overall_confidence = 0.0
            chain.final_answer = False
            chain.explanation = "No supporting evidence found"
    
    def _query_knowledge_base(self, subject: str, predicate: str, obj: str) -> List[Dict[str, Any]]:
        """Query the knowledge base for facts"""
        facts = []

        # If we have a knowledge base, query it
        if self.knowledge_base:
            concept = self.knowledge_base.get_concept(subject)
            if concept:
                if predicate == "can":
                    # Check if subject can perform the action
                    actions_data = self.knowledge_base.get_all_actions_for_subject(subject)
                    if obj in actions_data['all']:
                        source = 'direct' if obj in actions_data['direct'] else 'inherited'
                        facts.append({
                            "subject": subject,
                            "predicate": predicate,
                            "object": obj,
                            "confidence": 0.95 if source == 'direct' else 0.85,
                            "source": source
                        })
                elif predicate == "is":
                    # Check if subject has the property
                    properties_data = self.knowledge_base.get_all_properties_for_subject(subject)
                    if obj in properties_data['all']:
                        source = 'direct' if obj in properties_data['direct'] else 'inherited'
                        facts.append({
                            "subject": subject,
                            "predicate": predicate,
                            "object": obj,
                            "confidence": 0.95 if source == 'direct' else 0.85,
                            "source": source
                        })

        # If we have hierarchy manager, check hierarchy-level facts
        if self.hierarchy_manager and self.hierarchy_manager.is_valid_type(subject):
            if predicate == "can":
                # Check inherited actions from hierarchy
                inherited_actions = self.hierarchy_manager.get_inherited_actions(subject)
                if obj in inherited_actions:
                    facts.append({
                        "subject": subject,
                        "predicate": predicate,
                        "object": obj,
                        "confidence": 0.8,
                        "source": "hierarchy"
                    })
            elif predicate == "is":
                # Check inherited properties from hierarchy
                inherited_properties = self.hierarchy_manager.get_inherited_properties(subject)
                if obj in inherited_properties:
                    facts.append({
                        "subject": subject,
                        "predicate": predicate,
                        "object": obj,
                        "confidence": 0.8,
                        "source": "hierarchy"
                    })

        return facts
    
    def _get_parent_types(self, subject: str) -> List[str]:
        """Get parent types from hierarchy"""
        parent_types = []

        # First, try to get the subject's type from knowledge base
        if self.knowledge_base:
            concept = self.knowledge_base.get_concept(subject)
            if concept:
                subject_type = concept.get('type')
                if subject_type and self.hierarchy_manager:
                    # Get all ancestors of the subject's type
                    ancestors = self.hierarchy_manager.get_ancestors(subject_type)
                    parent_types.extend([subject_type] + ancestors)

        # If subject is itself a type in the hierarchy, get its ancestors
        if self.hierarchy_manager and self.hierarchy_manager.is_valid_type(subject):
            ancestors = self.hierarchy_manager.get_ancestors(subject)
            parent_types.extend(ancestors)

        # Remove duplicates while preserving order
        seen = set()
        unique_parents = []
        for parent in parent_types:
            if parent not in seen:
                seen.add(parent)
                unique_parents.append(parent)

        return unique_parents
    
    def _get_cache_key(self, query: str, context: Optional[Dict] = None) -> str:
        """Generate a cache key for the query"""
        context_str = json.dumps(context, sort_keys=True) if context else ""
        return f"{query}|{context_str}"
    
    def _update_statistics(self, chain: ReasoningChain):
        """Update reasoning statistics"""
        self.total_chains += 1
        
        if chain.final_answer and chain.overall_confidence > self.min_confidence:
            self.successful_chains += 1
        else:
            self.failed_chains += 1
        
        # Update average chain length
        total_steps = sum(len(c.steps) for c in [chain])  # This would track all chains in practice
        self.average_chain_length = total_steps / self.total_chains if self.total_chains > 0 else 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get reasoning engine statistics"""
        return {
            "total_chains": self.total_chains,
            "successful_chains": self.successful_chains,
            "failed_chains": self.failed_chains,
            "success_rate": self.successful_chains / self.total_chains if self.total_chains > 0 else 0,
            "average_chain_length": self.average_chain_length,
            "cache_size": len(self.reasoning_cache)
        }
    
    def clear_cache(self):
        """Clear the reasoning cache"""
        self.reasoning_cache.clear()
    
    def explain_reasoning(self, chain: ReasoningChain) -> str:
        """Generate a human-readable explanation of the reasoning chain"""
        if not chain.steps:
            return "No reasoning steps were performed."
        
        explanation_parts = [f"Question: {chain.query}"]
        
        for i, step in enumerate(chain.steps, 1):
            explanation_parts.append(f"Step {i}: {step.explanation}")
            if step.evidence:
                explanation_parts.append(f"  Evidence: {', '.join(str(e) for e in step.evidence)}")
        
        explanation_parts.append(f"\nConclusion: {chain.explanation}")
        explanation_parts.append(f"Confidence: {chain.overall_confidence:.2f}")
        explanation_parts.append(f"Answer: {'Yes' if chain.final_answer else 'No'}")
        
        return "\n".join(explanation_parts)