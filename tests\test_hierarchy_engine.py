#!/usr/bin/env python3
"""
Comprehensive Test Suite for Hierarchy-Aware Knowledge Engine V3
"""

import time
import json
from typing import Dict, Any, List
from knowledge_engine_v3 import HierarchyAwareKnowledgeEngine
from hierarchy_manager import HierarchyManager
from knowledge_base_v3 import HierarchyAwareKnowledgeBase

class HierarchyEngineTestSuite:
    """Test suite for hierarchy-aware knowledge engine"""
    
    def __init__(self):
        self.engine = None
        self.test_results = []
        self.performance_data = []
    
    def setup_engine(self) -> None:
        """Initialize the engine for testing"""
        print("🔧 Setting up Hierarchy-Aware Knowledge Engine...")
        try:
            self.engine = HierarchyAwareKnowledgeEngine()
            print("✅ Engine setup completed")
        except Exception as e:
            print(f"❌ Engine setup failed: {e}")
            raise
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test categories"""
        print("\n🧪 Starting Hierarchy-Aware Engine Test Suite")
        print("=" * 60)
        
        test_categories = [
            ("Hierarchy Management", self.test_hierarchy_management),
            ("Sentence Validation", self.test_sentence_validation),
            ("Learning with Hierarchy", self.test_learning_with_hierarchy),
            ("Question Answering", self.test_question_answering),
            ("Inheritance Features", self.test_inheritance_features),
            ("Type Relationships", self.test_type_relationships),
            ("Performance", self.test_performance),
            ("Error Handling", self.test_error_handling)
        ]
        
        for category_name, test_function in test_categories:
            print(f"\n📋 Testing {category_name}...")
            try:
                category_results = test_function()
                self.test_results.extend(category_results)
                passed = sum(1 for r in category_results if r['passed'])
                total = len(category_results)
                print(f"   ✅ {passed}/{total} tests passed")
            except Exception as e:
                print(f"   ❌ Category failed: {e}")
                self.test_results.append({
                    'category': category_name,
                    'test': 'category_execution',
                    'passed': False,
                    'error': str(e)
                })
        
        return self.generate_test_report()
    
    def test_hierarchy_management(self) -> List[Dict[str, Any]]:
        """Test hierarchy manager functionality"""
        results = []
        hm = self.engine.hierarchy_manager
        
        # Test 1: Valid type checking
        results.append(self._test_case(
            "Valid type checking",
            lambda: hm.is_valid_type('mammal'),
            expected=True
        ))
        
        # Test 2: Invalid type checking
        results.append(self._test_case(
            "Invalid type checking",
            lambda: hm.is_valid_type('invalid_type'),
            expected=False
        ))
        
        # Test 3: Subtype relationship
        results.append(self._test_case(
            "Subtype relationship (mammal is animal)",
            lambda: hm.is_subtype_of('mammal', 'animal'),
            expected=True
        ))
        
        # Test 4: Type hierarchy path
        results.append(self._test_case(
            "Type hierarchy path",
            lambda: 'entity' in hm.get_type_path('mammal'),
            expected=True
        ))
        
        # Test 5: Inherited properties
        results.append(self._test_case(
            "Inherited properties for living_thing",
            lambda: len(hm.get_inherited_properties('living_thing')) >= 0,
            expected=True
        ))
        
        return results
    
    def test_sentence_validation(self) -> List[Dict[str, Any]]:
        """Test sentence validation with hierarchy awareness"""
        results = []
        
        test_sentences = [
            ("cat can jump", True, "Basic action sentence"),
            ("dog is mammal", True, "Type assignment sentence"),
            ("bird can fly", True, "Species-specific action"),
            ("human is agent", True, "Agent type assignment"),
            ("stone can think", False, "Invalid action for object"),
            ("cat is location", False, "Invalid type assignment")
        ]
        
        for sentence, expected_valid, description in test_sentences:
            results.append(self._test_case(
                f"Validate: {description}",
                lambda s=sentence: self.engine.validate_sentence(s)['valid'],
                expected=expected_valid
            ))
        
        return results
    
    def test_learning_with_hierarchy(self) -> List[Dict[str, Any]]:
        """Test learning facts with hierarchy integration"""
        results = []
        
        # Test learning new concepts with types
        learning_tests = [
            ("tiger is mammal", "Type learning"),
            ("tiger can hunt", "Action learning"),
            ("eagle is bird", "Bird type learning"),
            ("eagle can soar", "Bird action learning"),
            ("robot is intelligent_system", "AI type learning")
        ]
        
        for sentence, description in learning_tests:
            results.append(self._test_case(
                f"Learn: {description}",
                lambda s=sentence: self.engine.learn_from_sentence(s)['success'],
                expected=True
            ))
        
        # Test inheritance after learning
        results.append(self._test_case(
            "Check inherited properties after learning",
            lambda: len(self.engine.knowledge_base.get_concept('tiger').get('inherited_properties', [])) > 0,
            expected=True
        ))
        
        return results
    
    def test_question_answering(self) -> List[Dict[str, Any]]:
        """Test question answering with hierarchy context"""
        results = []
        
        # Ensure we have some test data
        self.engine.learn_from_sentence("cat is mammal")
        self.engine.learn_from_sentence("cat can jump")
        self.engine.learn_from_sentence("dog is mammal")
        self.engine.learn_from_sentence("human is agent")
        
        question_tests = [
            ("what can cat do?", "Action capabilities query"),
            ("what is cat?", "Entity description query"),
            ("who is human?", "Agent identification query"),
            ("what type is cat?", "Type identification query"),
            ("how many mammal?", "Count query")
        ]
        
        for question, description in question_tests:
            results.append(self._test_case(
                f"Answer: {description}",
                lambda q=question: self.engine.answer_question(q)['success'],
                expected=True
            ))
        
        return results
    
    def test_inheritance_features(self) -> List[Dict[str, Any]]:
        """Test inheritance-specific features"""
        results = []
        
        # Setup test data
        self.engine.learn_from_sentence("lion is mammal")
        
        # Test inherited actions
        results.append(self._test_case(
            "Get all actions (direct + inherited)",
            lambda: len(self.engine.knowledge_base.get_all_actions_for_subject('lion')['all']) >= 0,
            expected=True
        ))
        
        # Test inherited properties
        results.append(self._test_case(
            "Get all properties (direct + inherited)",
            lambda: len(self.engine.knowledge_base.get_all_properties_for_subject('lion')['all']) >= 0,
            expected=True
        ))
        
        # Test action capability check
        results.append(self._test_case(
            "Check action capability",
            lambda: 'can_perform' in self.engine.knowledge_base.can_perform_action('lion', 'move'),
            expected=True
        ))
        
        # Test concept relationships
        results.append(self._test_case(
            "Get concept relationships",
            lambda: 'hierarchy_path' in self.engine.knowledge_base.get_concept_relationships('lion'),
            expected=True
        ))
        
        return results
    
    def test_type_relationships(self) -> List[Dict[str, Any]]:
        """Test type relationship queries"""
        results = []
        
        # Test finding concepts by hierarchy
        results.append(self._test_case(
            "Find concepts by hierarchy type",
            lambda: isinstance(self.engine.knowledge_base.find_concepts_by_hierarchy('animal', include_subtypes=True), list),
            expected=True
        ))
        
        # Test type validation
        results.append(self._test_case(
            "Type validation in learning",
            lambda: self.engine.knowledge_base.learn_fact_with_hierarchy('test_animal', 'type', 'mammal')['success'],
            expected=True
        ))
        
        # Test invalid type rejection
        results.append(self._test_case(
            "Invalid type rejection",
            lambda: not self.engine.knowledge_base.learn_fact_with_hierarchy('test_invalid', 'type', 'invalid_type')['success'],
            expected=True
        ))
        
        return results
    
    def test_performance(self) -> List[Dict[str, Any]]:
        """Test performance with hierarchy features"""
        results = []
        
        # Test sentence validation performance
        start_time = time.time()
        for i in range(10):
            self.engine.validate_sentence(f"test{i} can move")
        validation_time = time.time() - start_time
        
        results.append(self._test_case(
            "Validation performance (10 sentences < 1s)",
            lambda: validation_time < 1.0,
            expected=True
        ))
        
        self.performance_data.append({
            'test': 'validation_batch',
            'time': validation_time,
            'operations': 10
        })
        
        # Test question answering performance
        start_time = time.time()
        for i in range(5):
            self.engine.answer_question("what can cat do?")
        qa_time = time.time() - start_time
        
        results.append(self._test_case(
            "Question answering performance (5 questions < 1s)",
            lambda: qa_time < 1.0,
            expected=True
        ))
        
        self.performance_data.append({
            'test': 'qa_batch',
            'time': qa_time,
            'operations': 5
        })
        
        return results
    
    def test_error_handling(self) -> List[Dict[str, Any]]:
        """Test error handling and edge cases"""
        results = []
        
        # Test empty input handling
        results.append(self._test_case(
            "Handle empty sentence",
            lambda: not self.engine.validate_sentence("")['valid'],
            expected=True
        ))
        
        # Test invalid question format
        results.append(self._test_case(
            "Handle invalid question",
            lambda: not self.engine.answer_question("invalid question format")['success'],
            expected=True
        ))
        
        # Test learning from invalid sentence
        results.append(self._test_case(
            "Reject learning from invalid sentence",
            lambda: not self.engine.learn_from_sentence("invalid sentence structure")['success'],
            expected=True
        ))
        
        # Test unknown concept queries
        results.append(self._test_case(
            "Handle unknown concept query",
            lambda: not self.engine.answer_question("what is unknown_concept?")['success'],
            expected=True
        ))
        
        return results
    
    def _test_case(self, description: str, test_func, expected=True) -> Dict[str, Any]:
        """Execute a single test case"""
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            passed = (result == expected) if expected is not None else bool(result)
            
            return {
                'test': description,
                'passed': passed,
                'result': result,
                'expected': expected,
                'duration': duration,
                'error': None
            }
        except Exception as e:
            return {
                'test': description,
                'passed': False,
                'result': None,
                'expected': expected,
                'duration': 0,
                'error': str(e)
            }
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['passed'])
        failed_tests = total_tests - passed_tests
        
        # Calculate performance metrics
        total_duration = sum(r['duration'] for r in self.test_results)
        avg_duration = total_duration / total_tests if total_tests > 0 else 0
        
        # Group results by category
        categories = {}
        for result in self.test_results:
            category = result.get('category', 'Unknown')
            if category not in categories:
                categories[category] = {'passed': 0, 'total': 0}
            categories[category]['total'] += 1
            if result['passed']:
                categories[category]['passed'] += 1
        
        # Get engine status
        engine_status = self.engine.get_engine_status() if self.engine else {}
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                'total_duration': f"{total_duration:.3f}s",
                'average_duration': f"{avg_duration:.3f}s"
            },
            'categories': categories,
            'failed_tests': [r for r in self.test_results if not r['passed']],
            'performance_data': self.performance_data,
            'engine_status': engine_status,
            'hierarchy_features': {
                'inheritance_support': True,
                'type_validation': True,
                'semantic_hierarchy': True,
                'multi_level_queries': True
            }
        }
    
    def print_detailed_report(self, report: Dict[str, Any]) -> None:
        """Print detailed test report"""
        print("\n" + "=" * 60)
        print("🧪 HIERARCHY-AWARE ENGINE TEST REPORT")
        print("=" * 60)
        
        # Summary
        summary = report['summary']
        print(f"\n📊 Summary:")
        print(f"   Total Tests: {summary['total_tests']}")
        print(f"   Passed: {summary['passed']} ✅")
        print(f"   Failed: {summary['failed']} ❌")
        print(f"   Success Rate: {summary['success_rate']}")
        print(f"   Total Duration: {summary['total_duration']}")
        print(f"   Average Duration: {summary['average_duration']}")
        
        # Categories
        print(f"\n📋 Categories:")
        for category, stats in report['categories'].items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"   {category}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
        
        # Failed tests
        if report['failed_tests']:
            print(f"\n❌ Failed Tests:")
            for test in report['failed_tests']:
                print(f"   • {test['test']}")
                if test['error']:
                    print(f"     Error: {test['error']}")
                else:
                    print(f"     Expected: {test['expected']}, Got: {test['result']}")
        
        # Performance data
        if report['performance_data']:
            print(f"\n⚡ Performance:")
            for perf in report['performance_data']:
                ops_per_sec = perf['operations'] / perf['time'] if perf['time'] > 0 else 0
                print(f"   {perf['test']}: {perf['time']:.3f}s ({ops_per_sec:.1f} ops/sec)")
        
        # Hierarchy features
        print(f"\n🌳 Hierarchy Features:")
        for feature, enabled in report['hierarchy_features'].items():
            status = "✅" if enabled else "❌"
            print(f"   {feature}: {status}")
        
        # Engine status
        if 'engine_status' in report and report['engine_status']:
            engine_status = report['engine_status']
            print(f"\n🔧 Engine Status:")
            print(f"   Version: {engine_status.get('engine_version', 'Unknown')}")
            print(f"   Runtime: {engine_status.get('runtime', 'Unknown')}")
            
            if 'knowledge_base' in engine_status:
                kb_stats = engine_status['knowledge_base']
                print(f"   Concepts: {kb_stats.get('total_concepts', 0)}")
                print(f"   Learned Facts: {kb_stats.get('learned_facts', 0)}")
                print(f"   Inheritance Coverage: {kb_stats.get('inheritance_coverage', '0%')}")

def run_interactive_test() -> None:
    """Run interactive test mode"""
    print("🧪 Hierarchy-Aware Engine Interactive Test Mode")
    print("=" * 50)
    
    suite = HierarchyEngineTestSuite()
    suite.setup_engine()
    
    while True:
        print("\nTest Options:")
        print("1. Run all tests")
        print("2. Test specific category")
        print("3. Interactive engine test")
        print("4. Export test report")
        print("5. Exit")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == '1':
            report = suite.run_all_tests()
            suite.print_detailed_report(report)
        
        elif choice == '2':
            print("\nCategories:")
            categories = [
                "Hierarchy Management",
                "Sentence Validation", 
                "Learning with Hierarchy",
                "Question Answering",
                "Inheritance Features",
                "Type Relationships",
                "Performance",
                "Error Handling"
            ]
            
            for i, cat in enumerate(categories, 1):
                print(f"{i}. {cat}")
            
            try:
                cat_choice = int(input("Select category (1-8): ")) - 1
                if 0 <= cat_choice < len(categories):
                    # Run specific category test
                    print(f"\nRunning {categories[cat_choice]} tests...")
                    # This would need category-specific test methods
                else:
                    print("Invalid category selection")
            except ValueError:
                print("Invalid input")
        
        elif choice == '3':
            print("\nStarting interactive engine test...")
            suite.engine.interactive_mode()
        
        elif choice == '4':
            report = suite.run_all_tests()
            filename = f"hierarchy_test_report_{int(time.time())}.json"
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Test report exported to {filename}")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("Invalid option")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        run_interactive_test()
    else:
        # Run automated test suite
        suite = HierarchyEngineTestSuite()
        suite.setup_engine()
        report = suite.run_all_tests()
        suite.print_detailed_report(report)